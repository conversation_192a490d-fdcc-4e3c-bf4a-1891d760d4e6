{"PlayerBuildProgramLibrary.Data.BuiltFilesOutput": {"Files": ["Build/RegattaVision3D_Data/globalgamemanagers", "Build/RegattaVision3D_Data/globalgamemanagers.assets", "Build/RegattaVision3D_Data/globalgamemanagers.assets.resS", "Build/RegattaVision3D_Data/level0", "Build/RegattaVision3D_Data/level0.resS", "Build/RegattaVision3D_Data/level1", "Build/RegattaVision3D_Data/level1.resS", "Build/RegattaVision3D_Data/level2", "Build/RegattaVision3D_Data/level2.resS", "Build/RegattaVision3D_Data/resources.assets", "Build/RegattaVision3D_Data/resources.assets.resS", "Build/RegattaVision3D_Data/Resources/unity_builtin_extra", "Build/RegattaVision3D_Data/RuntimeInitializeOnLoads.json", "Build/RegattaVision3D_Data/ScriptingAssemblies.json", "Build/RegattaVision3D_Data/sharedassets0.assets", "Build/RegattaVision3D_Data/sharedassets0.assets.resS", "Build/RegattaVision3D_Data/sharedassets1.assets", "Build/RegattaVision3D_Data/sharedassets1.assets.resS", "Build/RegattaVision3D_Data/sharedassets1.resource", "Build/RegattaVision3D_Data/sharedassets2.assets", "Build/RegattaVision3D_Data/boot.config", "Build/RegattaVision3D_Data/StreamingAssets/meshtastic_gps_tracker.py", "Build/RegattaVision3D_Data/Plugins/x86_64/sqlite3.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.AIModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.ARModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.AccessibilityModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.AndroidJNIModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.AnimationModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.AssetBundleModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.AudioModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.ClothModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.ClusterInputModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.ClusterRendererModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.ContentLoadModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.CoreModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.CrashReportingModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.DSPGraphModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.DirectorModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.GIModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.GameCenterModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.GridModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.HotReloadModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.IMGUIModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.ImageConversionModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.InputLegacyModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.InputModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.JSONSerializeModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.LocalizationModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.NVIDIAModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.ParticleSystemModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.PerformanceReportingModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.Physics2DModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.PhysicsModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.ProfilerModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.PropertiesModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.ScreenCaptureModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.SharedInternalsModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.SpriteMaskModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.SpriteShapeModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.StreamingModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.SubstanceModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.SubsystemsModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.TLSModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.TerrainModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.TerrainPhysicsModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.TextCoreFontEngineModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.TextCoreTextEngineModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.TextRenderingModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.TilemapModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UIElementsModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UIModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UmbraModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UnityAnalyticsCommonModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UnityAnalyticsModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UnityConnectModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UnityCurlModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UnityTestProtocolModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestTextureModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestWWWModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.VFXModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.VRModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.VehiclesModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.VideoModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.VirtualTexturingModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.WindModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.XRModule.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.dll", "Build/RegattaVision3D_Data/Managed/Assembly-CSharp.dll", "Build/RegattaVision3D_Data/Managed/Unity.VisualEffectGraph.Runtime.dll", "Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Core.Runtime.dll", "Build/RegattaVision3D_Data/Managed/Unity.Postprocessing.Runtime.dll", "Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Universal.Config.Runtime.dll", "Build/RegattaVision3D_Data/Managed/Unity.VisualScripting.Flow.dll", "Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Core.ShaderLibrary.dll", "Build/RegattaVision3D_Data/Managed/Unity.AdaptivePerformance.Google.Android.dll", "Build/RegattaVision3D_Data/Managed/Unity.TextMeshPro.dll", "Build/RegattaVision3D_Data/Managed/Unity.Profiling.Core.dll", "Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Universal.Runtime.dll", "Build/RegattaVision3D_Data/Managed/Unity.Burst.dll", "Build/RegattaVision3D_Data/Managed/Unity.AdaptivePerformance.dll", "Build/RegattaVision3D_Data/Managed/Unity.VisualScripting.Core.dll", "Build/RegattaVision3D_Data/Managed/UnityEngine.UI.dll", "Build/RegattaVision3D_Data/Managed/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "Build/RegattaVision3D_Data/Managed/Unity.Timeline.dll", "Build/RegattaVision3D_Data/Managed/Unity.InputSystem.dll", "Build/RegattaVision3D_Data/Managed/Unity.InputSystem.ForUI.dll", "Build/RegattaVision3D_Data/Managed/Unity.Mathematics.dll", "Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Universal.Shaders.dll", "Build/RegattaVision3D_Data/Managed/Unity.VisualScripting.State.dll", "Build/RegattaVision3D_Data/Managed/Mapbox.VectorTile.Geometry.dll", "Build/RegattaVision3D_Data/Managed/Mapbox.VectorTile.PbfReader.dll", "Build/RegattaVision3D_Data/Managed/MapboxAccountsUnity.dll", "Build/RegattaVision3D_Data/Managed/M2Mqtt.Net.dll", "Build/RegattaVision3D_Data/Managed/Mapbox.Json.dll", "Build/RegattaVision3D_Data/Managed/Mapbox.IO.Compression.dll", "Build/RegattaVision3D_Data/Managed/Mapbox.VectorTile.VectorTileReader.dll", "Build/RegattaVision3D_Data/Managed/Mapbox.VectorTile.ExtensionMethods.dll", "Build/RegattaVision3D_Data/Managed/Unity.VisualScripting.Antlr3.Runtime.dll", "Build/RegattaVision3D_Data/Managed/Unity.Burst.Unsafe.dll", "Build/RegattaVision3D_Data/Managed/System.Runtime.dll", "Build/RegattaVision3D_Data/Managed/netstandard.dll", "Build/RegattaVision3D_Data/Managed/Mono.Security.dll", "Build/RegattaVision3D_Data/Managed/System.ComponentModel.Composition.dll", "Build/RegattaVision3D_Data/Managed/System.Configuration.dll", "Build/RegattaVision3D_Data/Managed/System.Core.dll", "Build/RegattaVision3D_Data/Managed/System.Data.DataSetExtensions.dll", "Build/RegattaVision3D_Data/Managed/System.Data.dll", "Build/RegattaVision3D_Data/Managed/System.Drawing.dll", "Build/RegattaVision3D_Data/Managed/System.EnterpriseServices.dll", "Build/RegattaVision3D_Data/Managed/System.IO.Compression.FileSystem.dll", "Build/RegattaVision3D_Data/Managed/System.IO.Compression.dll", "Build/RegattaVision3D_Data/Managed/System.Net.Http.dll", "Build/RegattaVision3D_Data/Managed/System.Numerics.dll", "Build/RegattaVision3D_Data/Managed/System.Runtime.Serialization.dll", "Build/RegattaVision3D_Data/Managed/System.Security.dll", "Build/RegattaVision3D_Data/Managed/System.ServiceModel.Internals.dll", "Build/RegattaVision3D_Data/Managed/System.Transactions.dll", "Build/RegattaVision3D_Data/Managed/System.Xml.Linq.dll", "Build/RegattaVision3D_Data/Managed/System.Xml.dll", "Build/RegattaVision3D_Data/Managed/System.dll", "Build/RegattaVision3D_Data/Managed/mscorlib.dll", "Build/RegattaVision3D_Data/Plugins/x86_64/lib_burst_generated.dll", "Build/RegattaVision3D_Data/app.info", "Build/RegattaVision3D_Data/Resources/unity default resources", "Build/RegattaVision3D.exe", "Build/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll", "Build/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll", "Build/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser", "Build/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx", "Build/MonoBleedingEdge/etc/mono/2.0/machine.config", "Build/MonoBleedingEdge/etc/mono/2.0/settings.map", "Build/MonoBleedingEdge/etc/mono/2.0/web.config", "Build/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser", "Build/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx", "Build/MonoBleedingEdge/etc/mono/4.0/machine.config", "Build/MonoBleedingEdge/etc/mono/4.0/settings.map", "Build/MonoBleedingEdge/etc/mono/4.0/web.config", "Build/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser", "Build/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx", "Build/MonoBleedingEdge/etc/mono/4.5/machine.config", "Build/MonoBleedingEdge/etc/mono/4.5/settings.map", "Build/MonoBleedingEdge/etc/mono/4.5/web.config", "Build/MonoBleedingEdge/etc/mono/browscap.ini", "Build/MonoBleedingEdge/etc/mono/config", "Build/MonoBleedingEdge/etc/mono/mconfig/config.xml", "Build/UnityCrashHandler64.exe", "Build/UnityPlayer.dll"], "BootConfigArtifact": "Library/Bee/artifacts/WinPlayerBuildProgram/boot.config"}}