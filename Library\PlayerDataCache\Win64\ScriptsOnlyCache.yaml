ScriptsOnlyBuild:
  usedScripts:
    Assembly-CSharp:
    - BoatWaypointMover
    - FloatingBoatUI
    - Mapbox.Examples.CameraBillboard
    - Mapbox.Examples.DragableDirectionWaypoint
    - Mapbox.Examples.FeatureUiMarker
    - Mapbox.Examples.LabelTextSetter
    - Mapbox.Examples.Ma<PERSON>Helper
    - Mapbox.Examples.PoiLabelTextSetter
    - Mapbox.Examples.PoiMarkerHelper
    - Mapbox.Examples.TextMeshOutline
    - Mapbox.Unity.MeshGeneration.Data.AtlasInfo
    - Mapbox.Unity.MeshGeneration.Modifiers.ModifierStack
    - Mapbox.Unity.MeshGeneration.Modifiers.PrefabModifier
    - Mapbox.Unity.MeshGeneration.Modifiers.ReplaceFeatureCollectionModifier
    - Mapbox.Unity.Utilities.DebugTools.SceneData
    - Mapbox.Unity.Utilities.DebugTools.ScenesList
    - RankingRow
    - ResultsRow
    - ScriptablePalette
    - TeamInfo
    - UIBoatEntry
    - UIRankingEntry
    - WaterTrailAdjuster
    Assembly-CSharp.dll:
    - AlertSystem
    - AppControl
    - BoatAlertHandler
    - BoatWaypointMover
    - CameraRotationController
    - FlagManager
    - FloatingBoatUI
    - GPSConfigurationManager
    - GPSDataFetcher
    - ManualTeleportTrigger
    - Mapbox.Examples.ForwardGeocodeUserInput
    - Mapbox.Examples.LoadingPanelController
    - Mapbox.Examples.QuadTreeCameraMovement
    - Mapbox.Examples.ReloadMap
    - Mapbox.Examples.SpawnOnMap
    - Mapbox.Unity.Map.AbstractMap
    - Mapbox.Unity.Map.MapVisualizer
    - Mapbox.Unity.Utilities.Runnable
    - MarkerManager
    - MeshtasticGPSTracker
    - MeshtasticUDPReceiver
    - PythonLauncher
    - RaceController
    - RaceResultsSaver
    - RaceTimer
    - RankingRow
    - RegattaCameraSwitcher
    - RegattaHelicopterCamera
    - RegattaTVCamera
    - RegattaTacticalCamera
    - ResultsRow
    - ResultsToggle
    - SceneChanger
    - SimpleWindController
    - SliderTextUpdater
    - SoundManager
    - StartBuoyPosition
    - StartSequenceTimer
    - StartingLine
    - TeamInfo
    - TrackerSettings
    - UIBoatEntry
    - UIBoatSelection
    - UIDisplayGeneralRanking
    - UIDisplayResults
    - UIRanking
    - UIRankingEntry
    - UI_PanelController
    - UnifiedBoatGPS
    - WaterTrailAdjuster
    Unity.AdaptivePerformance.Editor.dll:
    - UnityEditor.AdaptivePerformance.Editor.AdaptivePerformanceGeneralSettingsPerBuildTarget
    Unity.AdaptivePerformance.Google.Android.dll:
    - UnityEngine.AdaptivePerformance.Google.Android.GoogleAndroidProviderSettings
    Unity.AdaptivePerformance.Simulator.Editor.dll:
    - UnityEditor.AdaptivePerformance.Simulator.Editor.SimulatorProviderSettings
    Unity.AdaptivePerformance.dll:
    - UnityEngine.AdaptivePerformance.AdaptivePerformanceGeneralSettings
    - UnityEngine.AdaptivePerformance.AdaptivePerformanceManagerSettings
    Unity.InputSystem.dll:
    - UnityEngine.InputSystem.InputSettings
    - UnityEngine.InputSystem.InputSystemObject
    - UnityEngine.InputSystem.RemoteInputPlayerConnection
    Unity.RenderPipelines.Core.Runtime:
    - UnityEngine.Rendering.UI.DebugUIHandlerBitField
    - UnityEngine.Rendering.UI.DebugUIHandlerButton
    - UnityEngine.Rendering.UI.DebugUIHandlerCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerColor
    - UnityEngine.Rendering.UI.DebugUIHandlerContainer
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumField
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerFoldout
    - UnityEngine.Rendering.UI.DebugUIHandlerGroup
    - UnityEngine.Rendering.UI.DebugUIHandlerHBox
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerMessageBox
    - UnityEngine.Rendering.UI.DebugUIHandlerObject
    - UnityEngine.Rendering.UI.DebugUIHandlerObjectList
    - UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    - UnityEngine.Rendering.UI.DebugUIHandlerPanel
    - UnityEngine.Rendering.UI.DebugUIHandlerPersistentCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerProgressBar
    - UnityEngine.Rendering.UI.DebugUIHandlerRow
    - UnityEngine.Rendering.UI.DebugUIHandlerToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerToggleHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerVBox
    - UnityEngine.Rendering.UI.DebugUIHandlerValue
    - UnityEngine.Rendering.UI.DebugUIHandlerValueTuple
    - UnityEngine.Rendering.UI.DebugUIHandlerVector2
    - UnityEngine.Rendering.UI.DebugUIHandlerVector3
    - UnityEngine.Rendering.UI.DebugUIHandlerVector4
    - UnityEngine.Rendering.UI.UIFoldout
    Unity.RenderPipelines.Core.Runtime.dll:
    - UnityEngine.Rendering.Volume
    - UnityEngine.Rendering.VolumeProfile
    Unity.RenderPipelines.Universal.Editor.dll:
    - UnityEditor.Rendering.Universal.UniversalProjectSettings
    Unity.RenderPipelines.Universal.Runtime.dll:
    - UnityEngine.Rendering.Universal.Bloom
    - UnityEngine.Rendering.Universal.PostProcessData
    - UnityEngine.Rendering.Universal.Tonemapping
    - UnityEngine.Rendering.Universal.UniversalAdditionalCameraData
    - UnityEngine.Rendering.Universal.UniversalAdditionalLightData
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineGlobalSettings
    - UnityEngine.Rendering.Universal.UniversalRendererData
    - UnityEngine.Rendering.Universal.Vignette
    - UnityEngine.Rendering.Universal.XRSystemData
    Unity.Rider.Editor.dll:
    - Packages.Rider.Editor.UnitTesting.CallbackData
    Unity.TextMeshPro:
    - TMPro.TMP_ColorGradient
    - TMPro.TMP_FontAsset
    - TMPro.TMP_InputField
    - TMPro.TMP_Settings
    - TMPro.TMP_SpriteAsset
    - TMPro.TMP_StyleSheet
    - TMPro.TextMeshProUGUI
    Unity.TextMeshPro.dll:
    - TMPro.TMP_Dropdown
    - TMPro.TMP_FontAsset
    - TMPro.TMP_InputField
    - TMPro.TMP_Settings
    - TMPro.TMP_SpriteAsset
    - TMPro.TMP_StyleSheet
    - TMPro.TextMeshProUGUI
    Unity.VisualEffectGraph.Runtime.dll:
    - UnityEngine.VFX.VFXRuntimeResources
    UnityEditor.TestRunner.dll:
    - UnityEditor.TestTools.TestRunner.Api.CallbacksHolder
    - UnityEditor.TestTools.TestRunner.TestListCacheData
    - UnityEditor.TestTools.TestRunner.TestRun.TestJobDataHolder
    UnityEngine.UI:
    - UnityEngine.EventSystems.EventTrigger
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.ContentSizeFitter
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.HorizontalLayoutGroup
    - UnityEngine.UI.Image
    - UnityEngine.UI.LayoutElement
    - UnityEngine.UI.Mask
    - UnityEngine.UI.RectMask2D
    - UnityEngine.UI.ScrollRect
    - UnityEngine.UI.Scrollbar
    - UnityEngine.UI.Text
    - UnityEngine.UI.Toggle
    - UnityEngine.UI.VerticalLayoutGroup
    UnityEngine.UI.dll:
    - UnityEngine.EventSystems.EventSystem
    - UnityEngine.EventSystems.StandaloneInputModule
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.ContentSizeFitter
    - UnityEngine.UI.Dropdown
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.GridLayoutGroup
    - UnityEngine.UI.HorizontalLayoutGroup
    - UnityEngine.UI.Image
    - UnityEngine.UI.InputField
    - UnityEngine.UI.Mask
    - UnityEngine.UI.RectMask2D
    - UnityEngine.UI.ScrollRect
    - UnityEngine.UI.Scrollbar
    - UnityEngine.UI.Slider
    - UnityEngine.UI.Text
    - UnityEngine.UI.Toggle
    - UnityEngine.UI.VerticalLayoutGroup
  serializedClasses:
    Assembly-CSharp:
    - Mapbox.Unity.Map.CameraBoundsTileProviderOptions
    - Mapbox.Unity.Map.DefaultMapExtents
    - Mapbox.Unity.Map.EditorPreviewOptions
    - Mapbox.Unity.Map.ElevationLayerProperties
    - Mapbox.Unity.Map.ElevationModificationOptions
    - Mapbox.Unity.Map.ElevationRequiredOptions
    - Mapbox.Unity.Map.ImageryLayer
    - Mapbox.Unity.Map.ImageryLayerProperties
    - Mapbox.Unity.Map.ImageryRasterOptions
    - Mapbox.Unity.Map.LayerPerformanceOptions
    - Mapbox.Unity.Map.LayerSourceOptions
    - Mapbox.Unity.Map.MapExtentOptions
    - Mapbox.Unity.Map.MapLocationOptions
    - Mapbox.Unity.Map.MapOptions
    - Mapbox.Unity.Map.MapPlacementOptions
    - Mapbox.Unity.Map.MapScalingOptions
    - Mapbox.Unity.Map.RangeAroundTransformTileProviderOptions
    - Mapbox.Unity.Map.RangeTileProviderOptions
    - Mapbox.Unity.Map.SpawnPrefabOptions
    - Mapbox.Unity.Map.Style
    - Mapbox.Unity.Map.TerrainColliderOptions
    - Mapbox.Unity.Map.TerrainLayer
    - Mapbox.Unity.Map.TerrainSideWallOptions
    - Mapbox.Unity.Map.TileJsonData
    - Mapbox.Unity.Map.UnityLayerOptions
    - Mapbox.Unity.Map.VectorLayer
    - Mapbox.Unity.Map.VectorLayerProperties
    - Mapbox.Unity.MeshGeneration.Data.AtlasEntity
    - Mapbox.Unity.MeshGeneration.Modifiers.FeatureBundle
    - Mapbox.Utils.Vector2d
    - RegattaBoatData
    - TeamInfo/TeamNameEvent
    Unity.RenderPipelines.Core.Runtime:
    - UnityEngine.Rendering.BoolParameter
    - UnityEngine.Rendering.ClampedFloatParameter
    - UnityEngine.Rendering.ClampedIntParameter
    - UnityEngine.Rendering.ColorParameter
    - UnityEngine.Rendering.MinFloatParameter
    - UnityEngine.Rendering.TextureParameter
    - UnityEngine.Rendering.UI.DebugUIPrefabBundle
    - UnityEngine.Rendering.Vector2Parameter
    Unity.RenderPipelines.Universal.Runtime:
    - UnityEngine.Rendering.Universal.DownscaleParameter
    - UnityEngine.Rendering.Universal.HDRACESPresetParameter
    - UnityEngine.Rendering.Universal.NeutralRangeReductionModeParameter
    - UnityEngine.Rendering.Universal.PostProcessData/ShaderResources
    - UnityEngine.Rendering.Universal.PostProcessData/TextureResources
    - UnityEngine.Rendering.Universal.ScriptableRendererData/DebugShaderResources
    - UnityEngine.Rendering.Universal.StencilStateData
    - UnityEngine.Rendering.Universal.TemporalAA/Settings
    - UnityEngine.Rendering.Universal.TonemappingModeParameter
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset/TextureResources
    - UnityEngine.Rendering.Universal.UniversalRendererData/ShaderResources
    - UnityEngine.Rendering.Universal.XRSystemData/ShaderResources
    Unity.TextMeshPro:
    - TMPro.FaceInfo_Legacy
    - TMPro.FontAssetCreationSettings
    - TMPro.KerningTable
    - TMPro.TMP_Character
    - TMPro.TMP_Dropdown/DropdownEvent
    - TMPro.TMP_Dropdown/OptionData
    - TMPro.TMP_Dropdown/OptionDataList
    - TMPro.TMP_FontFeatureTable
    - TMPro.TMP_FontWeightPair
    - TMPro.TMP_GlyphAdjustmentRecord
    - TMPro.TMP_GlyphPairAdjustmentRecord
    - TMPro.TMP_GlyphValueRecord
    - TMPro.TMP_InputField/OnChangeEvent
    - TMPro.TMP_InputField/SelectionEvent
    - TMPro.TMP_InputField/SubmitEvent
    - TMPro.TMP_InputField/TextSelectionEvent
    - TMPro.TMP_InputField/TouchScreenKeyboardEvent
    - TMPro.TMP_Sprite
    - TMPro.TMP_SpriteCharacter
    - TMPro.TMP_SpriteGlyph
    - TMPro.TMP_Style
    - TMPro.VertexGradient
    UnityEngine.CoreModule:
    - UnityEngine.Events.ArgumentCache
    - UnityEngine.Events.PersistentCallGroup
    - UnityEngine.Events.PersistentListenerMode
    - UnityEngine.RectOffset
    UnityEngine.TextCoreFontEngineModule:
    - UnityEngine.TextCore.FaceInfo
    - UnityEngine.TextCore.Glyph
    - UnityEngine.TextCore.GlyphMetrics
    - UnityEngine.TextCore.GlyphRect
    UnityEngine.UI:
    - UnityEngine.EventSystems.EventTrigger/Entry
    - UnityEngine.EventSystems.EventTrigger/TriggerEvent
    - UnityEngine.UI.AnimationTriggers
    - UnityEngine.UI.Button/ButtonClickedEvent
    - UnityEngine.UI.ColorBlock
    - UnityEngine.UI.Dropdown/DropdownEvent
    - UnityEngine.UI.Dropdown/OptionData
    - UnityEngine.UI.Dropdown/OptionDataList
    - UnityEngine.UI.FontData
    - UnityEngine.UI.InputField/EndEditEvent
    - UnityEngine.UI.InputField/OnChangeEvent
    - UnityEngine.UI.InputField/SubmitEvent
    - UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
    - UnityEngine.UI.Navigation
    - UnityEngine.UI.ScrollRect/ScrollRectEvent
    - UnityEngine.UI.Scrollbar/ScrollEvent
    - UnityEngine.UI.Slider/SliderEvent
    - UnityEngine.UI.SpriteState
    - UnityEngine.UI.Toggle/ToggleEvent
  methodsToPreserve:
  - assembly: Assembly-CSharp
    fullTypeName: SceneChanger
    methodName: LoadScene
  - assembly: Assembly-CSharp
    fullTypeName: SceneChanger
    methodName: LoadScene
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerButton
    methodName: OnAction
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: OnScrollbarClicked
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: OnScrollbarClicked
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: ResetDebugManager
  - assembly: Assembly-CSharp
    fullTypeName: RaceController
    methodName: StartNewSession
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RaceController
    methodName: ManualStartNextSession
  - assembly: Assembly-CSharp
    fullTypeName: RaceController
    methodName: PrepareNextSession
  - assembly: Assembly-CSharp
    fullTypeName: StartSequenceTimer
    methodName: StartSequence
  - assembly: Assembly-CSharp
    fullTypeName: AppControl
    methodName: Quit
  - assembly: Assembly-CSharp
    fullTypeName: SceneChanger
    methodName: LoadScene
  - assembly: Assembly-CSharp
    fullTypeName: RaceController
    methodName: StopSession
  - assembly: Assembly-CSharp
    fullTypeName: SceneChanger
    methodName: LoadScene
  sceneClasses:
    Assets/Scenes/RegateScene.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 54
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 17118}
    - Class: 114
      Script: {instanceID: 17120}
    - Class: 114
      Script: {instanceID: 17124}
    - Class: 114
      Script: {instanceID: 17126}
    - Class: 114
      Script: {instanceID: 17128}
    - Class: 114
      Script: {instanceID: 17130}
    - Class: 114
      Script: {instanceID: 17134}
    - Class: 114
      Script: {instanceID: 17136}
    - Class: 114
      Script: {instanceID: 17800}
    - Class: 114
      Script: {instanceID: 17856}
    - Class: 114
      Script: {instanceID: 18418}
    - Class: 114
      Script: {instanceID: 19210}
    - Class: 114
      Script: {instanceID: 19764}
    - Class: 114
      Script: {instanceID: 19870}
    - Class: 114
      Script: {instanceID: 20538}
    - Class: 114
      Script: {instanceID: 20556}
    - Class: 114
      Script: {instanceID: 20668}
    - Class: 114
      Script: {instanceID: 20766}
    - Class: 114
      Script: {instanceID: 20784}
    - Class: 114
      Script: {instanceID: 20880}
    - Class: 114
      Script: {instanceID: 20938}
    - Class: 114
      Script: {instanceID: 21442}
    - Class: 114
      Script: {instanceID: 21654}
    - Class: 114
      Script: {instanceID: 21776}
    - Class: 114
      Script: {instanceID: 22254}
    - Class: 114
      Script: {instanceID: 22432}
    - Class: 114
      Script: {instanceID: 22544}
    - Class: 114
      Script: {instanceID: 22690}
    - Class: 114
      Script: {instanceID: 22758}
    - Class: 114
      Script: {instanceID: 22804}
    - Class: 114
      Script: {instanceID: 22886}
    - Class: 114
      Script: {instanceID: 23008}
    - Class: 114
      Script: {instanceID: 23066}
    - Class: 114
      Script: {instanceID: 23376}
    - Class: 114
      Script: {instanceID: 23518}
    - Class: 114
      Script: {instanceID: 23786}
    - Class: 114
      Script: {instanceID: 23822}
    - Class: 114
      Script: {instanceID: 24116}
    - Class: 114
      Script: {instanceID: 24254}
    - Class: 114
      Script: {instanceID: 24438}
    - Class: 114
      Script: {instanceID: 24498}
    - Class: 114
      Script: {instanceID: 24906}
    - Class: 114
      Script: {instanceID: 24920}
    - Class: 114
      Script: {instanceID: 25100}
    - Class: 114
      Script: {instanceID: 25510}
    - Class: 114
      Script: {instanceID: 25808}
    - Class: 114
      Script: {instanceID: 25844}
    - Class: 114
      Script: {instanceID: 26120}
    - Class: 114
      Script: {instanceID: 26238}
    - Class: 114
      Script: {instanceID: 26504}
    - Class: 114
      Script: {instanceID: 26552}
    - Class: 114
      Script: {instanceID: 26760}
    - Class: 114
      Script: {instanceID: 26892}
    - Class: 114
      Script: {instanceID: 27288}
    - Class: 114
      Script: {instanceID: 27348}
    - Class: 114
      Script: {instanceID: 27422}
    - Class: 114
      Script: {instanceID: 27514}
    - Class: 114
      Script: {instanceID: 27580}
    - Class: 114
      Script: {instanceID: 27824}
    - Class: 114
      Script: {instanceID: 28208}
    - Class: 114
      Script: {instanceID: 28614}
    - Class: 114
      Script: {instanceID: 28626}
    - Class: 114
      Script: {instanceID: 29150}
    - Class: 114
      Script: {instanceID: 29174}
    - Class: 114
      Script: {instanceID: 29506}
    - Class: 114
      Script: {instanceID: 29756}
    - Class: 114
      Script: {instanceID: 29812}
    - Class: 114
      Script: {instanceID: 35482}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 136
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 198
      Script: {instanceID: 0}
    - Class: 199
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 225
      Script: {instanceID: 0}
    Assets/Scenes/Secu.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 17118}
    - Class: 114
      Script: {instanceID: 17126}
    - Class: 114
      Script: {instanceID: 17128}
    - Class: 114
      Script: {instanceID: 17130}
    - Class: 114
      Script: {instanceID: 17134}
    - Class: 114
      Script: {instanceID: 18274}
    - Class: 114
      Script: {instanceID: 18536}
    - Class: 114
      Script: {instanceID: 20118}
    - Class: 114
      Script: {instanceID: 21654}
    - Class: 114
      Script: {instanceID: 23008}
    - Class: 114
      Script: {instanceID: 23436}
    - Class: 114
      Script: {instanceID: 23544}
    - Class: 114
      Script: {instanceID: 23612}
    - Class: 114
      Script: {instanceID: 24906}
    - Class: 114
      Script: {instanceID: 25100}
    - Class: 114
      Script: {instanceID: 25510}
    - Class: 114
      Script: {instanceID: 26120}
    - Class: 114
      Script: {instanceID: 27422}
    - Class: 114
      Script: {instanceID: 27514}
    - Class: 114
      Script: {instanceID: 28284}
    - Class: 114
      Script: {instanceID: 28412}
    - Class: 114
      Script: {instanceID: 28614}
    - Class: 114
      Script: {instanceID: 29174}
    - Class: 114
      Script: {instanceID: 29216}
    - Class: 114
      Script: {instanceID: 29294}
    - Class: 114
      Script: {instanceID: 29368}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    Assets/Scenes/SplashScreen.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 49
      Script: {instanceID: 0}
    - Class: 54
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 90
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 102
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 17118}
    - Class: 114
      Script: {instanceID: 17120}
    - Class: 114
      Script: {instanceID: 17124}
    - Class: 114
      Script: {instanceID: 17126}
    - Class: 114
      Script: {instanceID: 17128}
    - Class: 114
      Script: {instanceID: 17130}
    - Class: 114
      Script: {instanceID: 17134}
    - Class: 114
      Script: {instanceID: 17136}
    - Class: 114
      Script: {instanceID: 17138}
    - Class: 114
      Script: {instanceID: 17146}
    - Class: 114
      Script: {instanceID: 17462}
    - Class: 114
      Script: {instanceID: 17704}
    - Class: 114
      Script: {instanceID: 17772}
    - Class: 114
      Script: {instanceID: 17966}
    - Class: 114
      Script: {instanceID: 18066}
    - Class: 114
      Script: {instanceID: 18378}
    - Class: 114
      Script: {instanceID: 18938}
    - Class: 114
      Script: {instanceID: 19016}
    - Class: 114
      Script: {instanceID: 19180}
    - Class: 114
      Script: {instanceID: 19186}
    - Class: 114
      Script: {instanceID: 19764}
    - Class: 114
      Script: {instanceID: 19828}
    - Class: 114
      Script: {instanceID: 20710}
    - Class: 114
      Script: {instanceID: 20766}
    - Class: 114
      Script: {instanceID: 20904}
    - Class: 114
      Script: {instanceID: 20938}
    - Class: 114
      Script: {instanceID: 21102}
    - Class: 114
      Script: {instanceID: 21122}
    - Class: 114
      Script: {instanceID: 21170}
    - Class: 114
      Script: {instanceID: 21358}
    - Class: 114
      Script: {instanceID: 21646}
    - Class: 114
      Script: {instanceID: 21654}
    - Class: 114
      Script: {instanceID: 21728}
    - Class: 114
      Script: {instanceID: 21840}
    - Class: 114
      Script: {instanceID: 21856}
    - Class: 114
      Script: {instanceID: 21926}
    - Class: 114
      Script: {instanceID: 22354}
    - Class: 114
      Script: {instanceID: 22432}
    - Class: 114
      Script: {instanceID: 22886}
    - Class: 114
      Script: {instanceID: 23008}
    - Class: 114
      Script: {instanceID: 23380}
    - Class: 114
      Script: {instanceID: 23822}
    - Class: 114
      Script: {instanceID: 24006}
    - Class: 114
      Script: {instanceID: 24116}
    - Class: 114
      Script: {instanceID: 24438}
    - Class: 114
      Script: {instanceID: 24510}
    - Class: 114
      Script: {instanceID: 24670}
    - Class: 114
      Script: {instanceID: 24682}
    - Class: 114
      Script: {instanceID: 24906}
    - Class: 114
      Script: {instanceID: 24978}
    - Class: 114
      Script: {instanceID: 25100}
    - Class: 114
      Script: {instanceID: 25506}
    - Class: 114
      Script: {instanceID: 25510}
    - Class: 114
      Script: {instanceID: 25872}
    - Class: 114
      Script: {instanceID: 25942}
    - Class: 114
      Script: {instanceID: 26120}
    - Class: 114
      Script: {instanceID: 26140}
    - Class: 114
      Script: {instanceID: 26238}
    - Class: 114
      Script: {instanceID: 26364}
    - Class: 114
      Script: {instanceID: 26392}
    - Class: 114
      Script: {instanceID: 26504}
    - Class: 114
      Script: {instanceID: 26598}
    - Class: 114
      Script: {instanceID: 26656}
    - Class: 114
      Script: {instanceID: 26742}
    - Class: 114
      Script: {instanceID: 27288}
    - Class: 114
      Script: {instanceID: 27408}
    - Class: 114
      Script: {instanceID: 27444}
    - Class: 114
      Script: {instanceID: 27514}
    - Class: 114
      Script: {instanceID: 27652}
    - Class: 114
      Script: {instanceID: 27734}
    - Class: 114
      Script: {instanceID: 27900}
    - Class: 114
      Script: {instanceID: 28014}
    - Class: 114
      Script: {instanceID: 28354}
    - Class: 114
      Script: {instanceID: 28514}
    - Class: 114
      Script: {instanceID: 28614}
    - Class: 114
      Script: {instanceID: 28626}
    - Class: 114
      Script: {instanceID: 28718}
    - Class: 114
      Script: {instanceID: 28866}
    - Class: 114
      Script: {instanceID: 29026}
    - Class: 114
      Script: {instanceID: 29336}
    - Class: 114
      Script: {instanceID: 29812}
    - Class: 114
      Script: {instanceID: 30280}
    - Class: 114
      Script: {instanceID: 42448}
    - Class: 114
      Script: {instanceID: 42856}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 135
      Script: {instanceID: 0}
    - Class: 136
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 198
      Script: {instanceID: 0}
    - Class: 199
      Script: {instanceID: 0}
    - Class: 212
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 225
      Script: {instanceID: 0}
  scriptHashData:
  - hash:
      serializedVersion: 2
      Hash: bf362e053d05d8a86558bc2be3e03493
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRenderPipelineAsset
  - hash:
      serializedVersion: 2
      Hash: 540882762544409cc4530e60db00e951
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: CanvasScaler
  - hash:
      serializedVersion: 2
      Hash: d41ab9b5ea92474f9c0a6937a0607ce1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ContentSizeFitter
  - hash:
      serializedVersion: 2
      Hash: 33f160a5c07e580c29aba2bd1e4db811
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: HorizontalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: dd9dda1dfd39e605c49e72003342ef5d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Image
  - hash:
      serializedVersion: 2
      Hash: b5007673bbbdb9d12597a1d8201370f5
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Text
  - hash:
      serializedVersion: 2
      Hash: 915204dac8acad99f076d6e72ada0a0f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Button
  - hash:
      serializedVersion: 2
      Hash: 4878e735441f4041539853759559c86f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: VerticalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: d5cd7f207e91ca8336c65a577a6fb2a0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ScrollRect
  - hash:
      serializedVersion: 2
      Hash: 8dff8e9853297d341afa0c11f9091b59
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: LayoutElement
  - hash:
      serializedVersion: 2
      Hash: f235b66bbaad891803c9d777dfd67956
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveLut
  - hash:
      serializedVersion: 2
      Hash: ed2c59b75f8f3cd8df4cd4387d965e71
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector2
  - hash:
      serializedVersion: 2
      Hash: 55060a7bf226ac385ce19ebd450f199f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelRaycaster
  - hash:
      serializedVersion: 2
      Hash: 41fb16f752e4d05f3c72f3c7ae4deb87
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: LocationStatus
  - hash:
      serializedVersion: 2
      Hash: f45600bcc886206dd55a3208474aad61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollbarValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 00bbd9db8634496d079d17711b2ff7c4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Selectable
  - hash:
      serializedVersion: 2
      Hash: a416ac87241ee1ad4b43d8f355ab05a0
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: SnapTerrainRaycastModifier
  - hash:
      serializedVersion: 2
      Hash: 157d3cf40178d244cd11ae1c9791ead7
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.HID.Editor
    className: HIDDescriptorWindow
  - hash:
      serializedVersion: 2
      Hash: 89e2f93ac259634cf81b44c25bc0fc9a
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveShadowmapResolution
  - hash:
      serializedVersion: 2
      Hash: f2da708954b9eaf3c07c05f11164ff65
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: PoiLabelTextSetter
  - hash:
      serializedVersion: 2
      Hash: 95c8a8c68e664d4868755f06e7c55997
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: DepthOfField
  - hash:
      serializedVersion: 2
      Hash: 2f1f09a325279523c99f20de26eee540
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Components
    className: FeatureBehaviour
  - hash:
      serializedVersion: 2
      Hash: 65d7429f900cd0d7d58933027dfdd825
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: CompositeShadowCaster2D
  - hash:
      serializedVersion: 2
      Hash: a9a9985bc600ab81586355efc506ee17
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: WhiteBalance
  - hash:
      serializedVersion: 2
      Hash: 3050593c0e7ec98d4c0b1a69ff4cc4a7
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Location
    className: AngleSmoothingAverage
  - hash:
      serializedVersion: 2
      Hash: 559dd654ec5ea263b0abfe362cc3c113
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: HeroBuildingSelectionUserInput
  - hash:
      serializedVersion: 2
      Hash: 6b2b20d5352b9f1294595326f54bd3c2
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObjectPopupField
  - hash:
      serializedVersion: 2
      Hash: 5c518b3510ebb6ef6b151fd54baa09cb
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Location
    className: AngleSmoothingEMA
  - hash:
      serializedVersion: 2
      Hash: 745c234e274606053ecac394bbce2be6
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXUIDropdownBinder
  - hash:
      serializedVersion: 2
      Hash: 12e41435e8b3cda4e051fdf9129d96b3
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ManualBuoyPlacer
  - hash:
      serializedVersion: 2
      Hash: 2f888db183cfedb4a01392955b48efb0
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: MakiHelper
  - hash:
      serializedVersion: 2
      Hash: ed563a98eda89feb08d5226f728874a4
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: MeshtasticGPSTracker
  - hash:
      serializedVersion: 2
      Hash: ad49e20f31ed572e586aa8bbaafb591b
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropMessageListener
  - hash:
      serializedVersion: 2
      Hash: c256109474e689e4ef1848cb478a010a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UnifiedBoatGPS
  - hash:
      serializedVersion: 2
      Hash: b40f8ffcaa6b71545b8d6ff04b849814
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: AdvancedDropdownWindow
  - hash:
      serializedVersion: 2
      Hash: b77374c5f5659b85ddfc61de865cfc2f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RegattaCameraController
  - hash:
      serializedVersion: 2
      Hash: 158ea04b653ef20da50f27da42170155
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumePerSceneData
  - hash:
      serializedVersion: 2
      Hash: a09f0707ebc71b1f189be2976dd9f2b3
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RecordingExportUI
  - hash:
      serializedVersion: 2
      Hash: 21d1cbe15c85b7326807cbbca2ada9a6
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Map.TileProviders
    className: TileErrorHandler
  - hash:
      serializedVersion: 2
      Hash: b88e78b6860b6061b2c681ea9b80b6bf
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggle
  - hash:
      serializedVersion: 2
      Hash: 8708d1d279b86effd24ef979e9267390
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Location
    className: LocationArrayEditorLocationProvider
  - hash:
      serializedVersion: 2
      Hash: fce99de7ab700e0c4c466ebd583aa306
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ColorLookup
  - hash:
      serializedVersion: 2
      Hash: ed82ab63ed085eddf468bb7bd308e371
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: WindParticleController
  - hash:
      serializedVersion: 2
      Hash: 8cfa217dfa5c37a3c43dc78aa868ef30
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: LabelTextSetter
  - hash:
      serializedVersion: 2
      Hash: 75563a97b24811c153add23104e2638f
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: XRSystemData
  - hash:
      serializedVersion: 2
      Hash: 1321993db8249e1e05e1e0492b2bbdc3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameVisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: 373d7dc9f6269da4df29812b7e706d48
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Interfaces
    className: VectorLayerVisualizer
  - hash:
      serializedVersion: 2
      Hash: f88e3cb130102ccb13e538582ad15fb4
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: LoftModifier
  - hash:
      serializedVersion: 2
      Hash: af034974a323087c92c8b78a80aef49c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: MarkerManager
  - hash:
      serializedVersion: 2
      Hash: 04b6685b6f724c0d6024e6b6f96c8202
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: TimelineAsset
  - hash:
      serializedVersion: 2
      Hash: 7e12302f66e3c8ed5bcfea4ce65fccea
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityMessageListener
  - hash:
      serializedVersion: 2
      Hash: ed8bac09ffacadca9994789e381c69ce
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_TextSelector_A
  - hash:
      serializedVersion: 2
      Hash: 2541c03d117382d4ebbe0bb0775dd2dc
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerProgressBar
  - hash:
      serializedVersion: 2
      Hash: 3851b298bdb429888427e6274e04be2a
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeReferenceVolumeProfile
  - hash:
      serializedVersion: 2
      Hash: 755ec4bae2b3b16276059d8895b977ea
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveSorting
  - hash:
      serializedVersion: 2
      Hash: dbe4f05d59170430398f6453d7253fe5
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerWidget
  - hash:
      serializedVersion: 2
      Hash: 1c0d1e44ee8a8a8dfabd5d7c744b5bdb
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PythonLauncher
  - hash:
      serializedVersion: 2
      Hash: fce9b835d1e8b08e7ecea8da19f21986
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: CoroutineRunner
  - hash:
      serializedVersion: 2
      Hash: fe111e830148f07e5735ebe7620f752e
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: ChromaticAberration
  - hash:
      serializedVersion: 2
      Hash: f68cd1787d69dfe940e97230ea2ee1af
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXPlaneBinder
  - hash:
      serializedVersion: 2
      Hash: b2afee545f7607bd2d2b5a2ae227d428
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveShadowDistance
  - hash:
      serializedVersion: 2
      Hash: 28fd3d045acc780719a17f02073eb448
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: InputField
  - hash:
      serializedVersion: 2
      Hash: 07f6b7f433c8738108c47c5dd5c7c7cd
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptivePhysics
  - hash:
      serializedVersion: 2
      Hash: 6d394de691f6d0187f61c08889353d0e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnButtonClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1eb5ae67767c75192483770d751f385e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BlinkLight
  - hash:
      serializedVersion: 2
      Hash: c77e7f80798b9c346f7ef512d203b0e5
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveBatching
  - hash:
      serializedVersion: 2
      Hash: f33b95fe9aa5580c1ff792b8447cc04e
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXUIToggleBinder
  - hash:
      serializedVersion: 2
      Hash: b98001682cba83786775c9e415b89a61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropdownValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: b838cbada0b03f1cfbaebc8e124f4f39
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenButton
  - hash:
      serializedVersion: 2
      Hash: 1d830bc5cfaaf24301dffea2877f4bea
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexShakeA
  - hash:
      serializedVersion: 2
      Hash: aa6623e19c6d8364fa7ccbec13f130b4
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveFramerate
  - hash:
      serializedVersion: 2
      Hash: 36afd2b3383c61ddf671e7c29c69c388
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputMouseBinder
  - hash:
      serializedVersion: 2
      Hash: d90629714fa6b0145fcaffc87f5c6559
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: bb8d968ef4474e71aaad4a4dce279f90
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: b6307dfc54cca9ba44f47185c578b0ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 831a07ec3647706fc718cb04ae39f2c9
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXPropertyBinder
  - hash:
      serializedVersion: 2
      Hash: 9f355e210e7950e47ad1391c7681435b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumField
  - hash:
      serializedVersion: 2
      Hash: 0f723e8457ba62c9101c2f7a507a3867
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: 
    className: VisualEffectActivationClip
  - hash:
      serializedVersion: 2
      Hash: 99431ab619f390cf9400bd06d3e1bdd3
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRenderPipelineGlobalSettings
  - hash:
      serializedVersion: 2
      Hash: 3b5eded99025c463a83ed313d009f690
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Map.TileProviders
    className: QuadTreeMapVisualizer
  - hash:
      serializedVersion: 2
      Hash: 5c21f4846181ee6e245db1fcfb62fc0e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerContainer
  - hash:
      serializedVersion: 2
      Hash: 39ce22f88c4c9c795740cdb54f992ad6
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputTouchBinder
  - hash:
      serializedVersion: 2
      Hash: 7bdff9e91edfd9600f92a185def7e34a
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptMachine
  - hash:
      serializedVersion: 2
      Hash: bf0cf6627bb60e4f796cfa748e67d1c2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GPSWaypointIntegrator
  - hash:
      serializedVersion: 2
      Hash: d5b532d6806f08f390094b3fe075023c
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples.Playground
    className: ForwardGeocoderExample
  - hash:
      serializedVersion: 2
      Hash: 43ebd945b04bced376c791411cf5a289
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSelectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0891ec0745e7e4f335fe7b881d7177fd
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GPSRigidbodyFixer
  - hash:
      serializedVersion: 2
      Hash: 006cf428550f7af7c78bbd72b6f9c495
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: DecalRendererFeature
  - hash:
      serializedVersion: 2
      Hash: 06840162b6a26a1e441506e4c83b257f
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: ModifierStack
  - hash:
      serializedVersion: 2
      Hash: f5df081962c06a171f380eb236cd1f3c
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVBox
  - hash:
      serializedVersion: 2
      Hash: 418b67019f13f9684552f2693bae3971
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TextMeshProFloatingText
  - hash:
      serializedVersion: 2
      Hash: 0eda0af872d5c0bc6c04d61bb8a307a6
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: StartingLine
  - hash:
      serializedVersion: 2
      Hash: 960935b102b3064f4b924de16426e340
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshPro
  - hash:
      serializedVersion: 2
      Hash: b2d2a01a2a85d9ed12e80cb33a9a6044
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationTrack
  - hash:
      serializedVersion: 2
      Hash: 6217b1287eca7eed313cf0b864249a30
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 854f2a7399990c64728c240ab7ec221e
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: SpawnOnGlobeExample
  - hash:
      serializedVersion: 2
      Hash: ee5e76b9ce22c8b3b1534a08f39fe1cb
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Location
    className: EditorLocationProviderLocationLog
  - hash:
      serializedVersion: 2
      Hash: 61991a5d56f8f357611a4175508746a3
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: AstronautMouseController
  - hash:
      serializedVersion: 2
      Hash: 4ffb0e18a540423f0276bd2bc2b917b0
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GPSToWaypointAdapter
  - hash:
      serializedVersion: 2
      Hash: 0a3f328f3ea906bbbcf794f4c27ee59d
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptGraphAsset
  - hash:
      serializedVersion: 2
      Hash: e4b7831f0a9e91f079eb40ea24427631
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ColorAdjustments
  - hash:
      serializedVersion: 2
      Hash: 72561d2e822b466169cd12a3b9f9ae0c
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexShakeB
  - hash:
      serializedVersion: 2
      Hash: fb79e95da8891fc7ac6c36ca6967af23
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationTrack
  - hash:
      serializedVersion: 2
      Hash: 552dca338f20933ce362ebbe559a6aa6
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1deda86fe7ffd8587afcfe68a02d034c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: EnvMapAnimator
  - hash:
      serializedVersion: 2
      Hash: 95426d463074947dcbffc797c44e779c
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: StandaloneInputModuleModuleEditor
  - hash:
      serializedVersion: 2
      Hash: d830083c72372737285397d1e384faac
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PositionYMonitor
  - hash:
      serializedVersion: 2
      Hash: cb9ce9f1ee1a061811c090f653641514
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SimpleBuoyUI
  - hash:
      serializedVersion: 2
      Hash: 5835aa044b7ea3413cc6d9d819e95dd9
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 7dc4ea0a6a9dbb02d52a9f70b8d95bb9
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Factories
    className: VectorTileFactory
  - hash:
      serializedVersion: 2
      Hash: 2c06f2f3416275f8b358305dc360e6ee
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BoatGPSManager
  - hash:
      serializedVersion: 2
      Hash: 682dfc653b22feea53b38adb3bc66414
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalReceiver
  - hash:
      serializedVersion: 2
      Hash: 85c5e49d66c19faeebc498d20edbbf82
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: WaterTrailAdjuster
  - hash:
      serializedVersion: 2
      Hash: 919dc7ec08f773fcae2d21a1e018bd99
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: MotionBlur
  - hash:
      serializedVersion: 2
      Hash: fe0a072dbdcbca34d5003c5299de3aaf
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: SceneRenderPipeline
  - hash:
      serializedVersion: 2
      Hash: c933b590b3badb5918fa01e73cd5cc0c
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Settings
  - hash:
      serializedVersion: 2
      Hash: ca7965796a6fc0e5baad4cb19f5b5ded
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreak2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: e416782d95a812c5b5b00e9b103a8e94
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: CameraController
  - hash:
      serializedVersion: 2
      Hash: 9f27b94c3c17b0da0d39632b61a4b711
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Bloom
  - hash:
      serializedVersion: 2
      Hash: 9182809856cd35c5e99ac5d54694e10f
    assemblyName: Assembly-CSharp
    namespaceName: Bitgem.VFX.StylisedWater
    className: WaterVolumeBox
  - hash:
      serializedVersion: 2
      Hash: 2281b3c22c89248081386c81024c558d
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration
    className: FeatureCollectionBase
  - hash:
      serializedVersion: 2
      Hash: 215d2dc6ec6ea06728398ea39a103cb3
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenStick
  - hash:
      serializedVersion: 2
      Hash: 9f92e751d2286296361a1d06b9e90c1e
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: MotionBlur
  - hash:
      serializedVersion: 2
      Hash: b56a15e48b82e47365a73e10a73699ba
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: SpawnOnMap
  - hash:
      serializedVersion: 2
      Hash: 96e7bcfd2d072992dab64ac44058b930
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCancelMessageListener
  - hash:
      serializedVersion: 2
      Hash: 52840a82fb83b3a7df342e354c4815e4
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMPro_InstructionOverlay
  - hash:
      serializedVersion: 2
      Hash: a75a568ce902366f4e82fc98ca6108f5
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_ExampleScript_01
  - hash:
      serializedVersion: 2
      Hash: f59871b1e5287ea33d7da0bd57845a38
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Components
    className: VertexDebuggerGizmo
  - hash:
      serializedVersion: 2
      Hash: dc31d211c7459841fe4f406ae2e51480
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TextConsoleSimulator
  - hash:
      serializedVersion: 2
      Hash: 8fe6d4f095ce838f5396d1867140a9b6
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: CameraSwitcher
  - hash:
      serializedVersion: 2
      Hash: 7e9cad7f71f9cc52f699bbd2d2a75734
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: c307188b084d7bae2b5fe7ace22248fb
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRenderPipelineEditorResources
  - hash:
      serializedVersion: 2
      Hash: ffaaa6b08bc8a8928411e8e402432358
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: PostProcessVolume
  - hash:
      serializedVersion: 2
      Hash: aa349b22cd61b9e8488d736f82590d6b
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_TextInfoDebugTool
  - hash:
      serializedVersion: 2
      Hash: e88d71ef93aba4e7faf243430f6a10ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: MacroScriptableObject
  - hash:
      serializedVersion: 2
      Hash: 8dc40a01667ca02b3354a4e7a61be082
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GridLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 90cf308600fb4aeb677786843453ec55
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: RemoteInputPlayerConnection
  - hash:
      serializedVersion: 2
      Hash: fb2207f5b331463a344622fa3a98462c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: MeshtasticUDPReceiver
  - hash:
      serializedVersion: 2
      Hash: b6792907b61f32faaf3e23f55e750107
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: PostProcessResources
  - hash:
      serializedVersion: 2
      Hash: db9b308769d19e1f9e161df0392c23ca
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformChildrenChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 6d94138b60a9299798dda96c1ee6725f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBeginDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 415316b9bbc4e339c901a46503e2790b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: VolumeProfile
  - hash:
      serializedVersion: 2
      Hash: c833d8ddc320fe5153f6a88cef23c858
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObject
  - hash:
      serializedVersion: 2
      Hash: faaf56bbfffb476c8d3fa5f40e735890
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: TerrainDataFetcher
  - hash:
      serializedVersion: 2
      Hash: 3aaeb3c96a9fcc3336568d8029680640
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: POIPlacementScriptExample
  - hash:
      serializedVersion: 2
      Hash: 8f877ac5abe9c9a18166da9409f1b119
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXPositionBinder
  - hash:
      serializedVersion: 2
      Hash: 10c17b309bdca62b4d7fbaf113ed4ca0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Toggle
  - hash:
      serializedVersion: 2
      Hash: 48a303d6887717f697b648e67480da84
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UIDisplayGeneralRanking
  - hash:
      serializedVersion: 2
      Hash: 9ed84ccc7f822799f8d08159e2a3e77d
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXMouseEventBinder
  - hash:
      serializedVersion: 2
      Hash: aede9640da9649462a43fd464d84996d
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UniAndroidPermission
  - hash:
      serializedVersion: 2
      Hash: 191cd500c627cd6682ef6f70b8e47f2c
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: b4e6aabdb5cd218e124308b4a9c87e42
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BoatAlertHandler
  - hash:
      serializedVersion: 2
      Hash: 5f70b16cd9acc9c1dd88b51b6ed89669
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectToggle
  - hash:
      serializedVersion: 2
      Hash: 3bb6398e3c76af28f243fa5c7400179d
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ImprovedTeleportationManager
  - hash:
      serializedVersion: 2
      Hash: 829aa9cf6f889f9b9c48692604b71a84
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UIRankingEntry
  - hash:
      serializedVersion: 2
      Hash: 669b82fe87a790965c2979cda99de0f5
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: ImmediatePositionWithLocationProvider
  - hash:
      serializedVersion: 2
      Hash: c65fb8f721cc4462e7a642974c37d3c0
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: DepthOfField
  - hash:
      serializedVersion: 2
      Hash: 67acb1248c92fa99edc55d81bdff40d5
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: LayerModifier
  - hash:
      serializedVersion: 2
      Hash: be643a0a4d647cb5696c4feebba95a24
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptivePerformanceManagerSpawner
  - hash:
      serializedVersion: 2
      Hash: 68bcdced790af1f01c423c6eb53fe5a0
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RegattaSimulator
  - hash:
      serializedVersion: 2
      Hash: 0dd14e314956b0d0d83ffcffe60aa46f
    assemblyName: Assembly-CSharp
    namespaceName: TMPro
    className: TMP_DigitValidator
  - hash:
      serializedVersion: 2
      Hash: 25f9856e61afbe27958849616102671e
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveShadowCascade
  - hash:
      serializedVersion: 2
      Hash: 400e142f29872d00e0401f3f2e8f5b0f
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Utilities
    className: Runnable
  - hash:
      serializedVersion: 2
      Hash: 3e1ba87a30ba656beab510dc2fa7cdc3
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumeAsset
  - hash:
      serializedVersion: 2
      Hash: 5874b789cd9832847c61ebfa803213af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelEventHandler
  - hash:
      serializedVersion: 2
      Hash: e8fd74e57e72e8e4ef28792881adb864
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerUIntField
  - hash:
      serializedVersion: 2
      Hash: 58570e231c93c2f96918856759b81286
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPanel
  - hash:
      serializedVersion: 2
      Hash: 9dd54ce33440072d6692a67e0c384ad1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: GroupTrack
  - hash:
      serializedVersion: 2
      Hash: 5c2a5af4658ede896d072a8a641d706e
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ColorCurves
  - hash:
      serializedVersion: 2
      Hash: bf0842d88681628b29beb00b5e2ab785
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDeselectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 579fbe361ad41dd4f5a78c35f8cda16e
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: CameraBillboard
  - hash:
      serializedVersion: 2
      Hash: 1af7e9f99d251dfa1961d4b644ced380
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Experimental.Rendering.Universal
    className: PixelPerfectCamera
  - hash:
      serializedVersion: 2
      Hash: a9f0153fc675e1f4ade818b83e4ff0c1
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: VolumeComponent
  - hash:
      serializedVersion: 2
      Hash: 0f66458b192187e4b19dd184e055c978
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: FeatureUiMarker
  - hash:
      serializedVersion: 2
      Hash: d120118d6292af2107e6017d0606727d
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples.Scripts.Utilities
    className: DragRotate
  - hash:
      serializedVersion: 2
      Hash: eae098d2a2f59438a9684f6c9d02a992
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: FlagManager
  - hash:
      serializedVersion: 2
      Hash: 209c1378f23c45375951c1715943159f
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXTriggerEventBinder
  - hash:
      serializedVersion: 2
      Hash: 25aa81c6b912712f2efc7f9a9a1d060e
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: FeatureBehaviourModifier
  - hash:
      serializedVersion: 2
      Hash: 5e3a401cc0ce4a5b3b98ad809c8601ae
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: HeightModifier
  - hash:
      serializedVersion: 2
      Hash: 4166580c620718548959a77fa7b43fc1
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TextMeshSpawner
  - hash:
      serializedVersion: 2
      Hash: 0e663c222bb83f92bce24cf73a0e299f
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeTouchupVolume
  - hash:
      serializedVersion: 2
      Hash: ef38c1407dba8c1f5d69f5db61a694fe
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: AnimatorMessageListener
  - hash:
      serializedVersion: 2
      Hash: 47f78e94e3df8d612cefa1307a19e2ce
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerValue
  - hash:
      serializedVersion: 2
      Hash: 9637dcdf3068a91dfe653e61bb82f13d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshProUGUI
  - hash:
      serializedVersion: 2
      Hash: c0a2b4d13675f08bfb211e044c334e68
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIntField
  - hash:
      serializedVersion: 2
      Hash: d771cabeaa363771c54f6c41824f7abb
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: LiftGammaGain
  - hash:
      serializedVersion: 2
      Hash: b56892a5eaf48e628c1db688add8b53b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UIDisplayResults
  - hash:
      serializedVersion: 2
      Hash: c1e224ab22649199198870bc9f22b2cc
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples.Playground
    className: DirectionsExample
  - hash:
      serializedVersion: 2
      Hash: cde402e184a6ab530144506bc9d6a0af
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector4
  - hash:
      serializedVersion: 2
      Hash: cb38ad65512dda78ac53556140b6df61
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: ReplaceFeatureCollectionModifier
  - hash:
      serializedVersion: 2
      Hash: cb49ce80d4b69cb2ed30d83bfcb146ae
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerBitField
  - hash:
      serializedVersion: 2
      Hash: 4f353524ce6564e40764f1ea080b2d85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 17abd56571de68c47519b8d4c6477967
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: StandingEntryUI
  - hash:
      serializedVersion: 2
      Hash: 026a577b8baa11e6202524d5c176d148
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: VisualEffectControlClip
  - hash:
      serializedVersion: 2
      Hash: 090b4014813624206b1713d7decd7b0d
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputKeyBinder
  - hash:
      serializedVersion: 2
      Hash: 13f262cc0741566a7ab72682984bc3e1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMesh
  - hash:
      serializedVersion: 2
      Hash: 672716ae97f6ddb615fa5ac3ec9da349
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools
    className: BeforeAfterTestCommandState
  - hash:
      serializedVersion: 2
      Hash: bc75926bfd3609757f7bf33ff766f026
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.EnhancedTouch
    className: TouchSimulation
  - hash:
      serializedVersion: 2
      Hash: 92dd8577d822a7e043cc1c00bacdd956
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Utilities
    className: MapVisualizerPerformance
  - hash:
      serializedVersion: 2
      Hash: 31f81b6966276e04e85e8ea09e1e0ea5
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: AstronautDirections
  - hash:
      serializedVersion: 2
      Hash: f7592abcee537d7d6307b069cbb530d7
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Utilities
    className: OpenUrlOnButtonClick
  - hash:
      serializedVersion: 2
      Hash: b6b0d9f9e983e4b0f2dafc74a05dce46
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UIRanking
  - hash:
      serializedVersion: 2
      Hash: e63fe08659e7e4647a51098c666f8845
    assemblyName: Unity.InputSystem
    namespaceName: 
    className: DownloadableSample
  - hash:
      serializedVersion: 2
      Hash: f669be52680c88846b2093fd41d1e06e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: e88e86abced569a14f48fc7c1def7882
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: CameraMovement
  - hash:
      serializedVersion: 2
      Hash: 20a120e55ad3ab65556328d5fd8309dd
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Outline
  - hash:
      serializedVersion: 2
      Hash: 2deddcdd5f664164bb803747210cbba2
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerRow
  - hash:
      serializedVersion: 2
      Hash: 33eb495d99aa9bea4dbe8b4c4e02c7bb
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputActionReference
  - hash:
      serializedVersion: 2
      Hash: 47247e29ff0f727264f83a7f754b229c
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: LensFlareDataSRP
  - hash:
      serializedVersion: 2
      Hash: e87009ccd2b5eab9e3f5055c461d13bf
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: UvModifier
  - hash:
      serializedVersion: 2
      Hash: e0cf060d3a0d1afae0c661ac42606ab8
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolume
  - hash:
      serializedVersion: 2
      Hash: 759504a58cb324f92f33268f9d012949
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ResultsRow
  - hash:
      serializedVersion: 2
      Hash: dc491bdaff17ab31ea53e6e914c37925
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXPreviousPositionBinder
  - hash:
      serializedVersion: 2
      Hash: 6be7d1b943f75fabca882c2b0a27c3c2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: MeshGenerationBase
  - hash:
      serializedVersion: 2
      Hash: d2ecd94550edf1b56aff07b1f4e41c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8668b70b18eb7f5cd2dce04386848007
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexJitter
  - hash:
      serializedVersion: 2
      Hash: 9be97b0b1ee94c99757de77366b30c65
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RaceResultsSaver
  - hash:
      serializedVersion: 2
      Hash: e8d390bd9ecacc453a7500b90d0c0292
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: Physics2DRaycaster
  - hash:
      serializedVersion: 2
      Hash: a32b44d78e884a8d1b2602dd3b801e7b
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: SnapTerrainModifier
  - hash:
      serializedVersion: 2
      Hash: afb7b65d28d5a7423e69a1b076be0064
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: PositionAsUV1
  - hash:
      serializedVersion: 2
      Hash: c036e6df7095343bad60a257add3b7f8
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InitializationTeleporter
  - hash:
      serializedVersion: 2
      Hash: 04f2d6a4db8a670aeb2c87d9ffd08865
    assemblyName: Assembly-CSharp
    namespaceName: TMPro
    className: TMP_TextEventHandler
  - hash:
      serializedVersion: 2
      Hash: d16d433db17c74d6fbaf1590f30bb90e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: FirebaseGPSTracker
  - hash:
      serializedVersion: 2
      Hash: b278bd6b181235e59e3ad57ec0233921
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RegattaTacticalCamera
  - hash:
      serializedVersion: 2
      Hash: c1ed73b795618e5f56e317d1e5bccb3d
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: CharacterMovement
  - hash:
      serializedVersion: 2
      Hash: 3c667a9d1ae3d1423a38693dea61485f
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: HighlightFeature
  - hash:
      serializedVersion: 2
      Hash: 4895209f48657ff949db7815a6062743
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputSettings
  - hash:
      serializedVersion: 2
      Hash: a89b0448af4e1cd103a77f9fec0a961f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSubmitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1a46629d5ec5009883efbe630a6b52e3
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveLayerCulling
  - hash:
      serializedVersion: 2
      Hash: b64d65c657ad8d5e94d1b753db33985a
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: MeshModifier
  - hash:
      serializedVersion: 2
      Hash: 5ebc58ec194dd44630b25fcc270ad4af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Dropdown
  - hash:
      serializedVersion: 2
      Hash: da732d9a59f7916982dbfd565ab019c1
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UI_PanelController
  - hash:
      serializedVersion: 2
      Hash: 61268ab592453a893888fb08f8428c3f
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: LoopAndDelay
  - hash:
      serializedVersion: 2
      Hash: 4728e3d21cc2a12e391e71ec9b7eed0b
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalTrack
  - hash:
      serializedVersion: 2
      Hash: 2c6d89da9166eb0e53f08434e1413ff8
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RaceSession
  - hash:
      serializedVersion: 2
      Hash: b2ccdb606f1518dc6ba4d53bd94a474e
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: AddToCollectionModifier
  - hash:
      serializedVersion: 2
      Hash: 3619bbe901cd3d551eb5ea7cc6882b89
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXAudioSpectrumBinder
  - hash:
      serializedVersion: 2
      Hash: fa991808d737c730189bf1d1e86c0e18
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: ChamferHeightModifier
  - hash:
      serializedVersion: 2
      Hash: 26b7910ed77507010863951badbe50a7
    assemblyName: Unity.AdaptivePerformance.Google.Android
    namespaceName: UnityEngine.AdaptivePerformance.Google.Android
    className: GoogleAndroidProviderLoader
  - hash:
      serializedVersion: 2
      Hash: 0bb0c119a1323042be293b8d6bef7011
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Map
    className: UpdateMapWithLocationProvider
  - hash:
      serializedVersion: 2
      Hash: 8738af596a67d389200f6546e33dcaeb
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Location
    className: TransformLocationProvider
  - hash:
      serializedVersion: 2
      Hash: 88b6571baf16c1c5586fe6420755ca09
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UIBoatEntry
  - hash:
      serializedVersion: 2
      Hash: de43dbe2a8c532f5fc756f01a6876b1e
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: PolygonMeshModifier
  - hash:
      serializedVersion: 2
      Hash: f2e2fc20d4e9e96d9dd76b68ab028ff5
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXSphereBinder
  - hash:
      serializedVersion: 2
      Hash: 22baa8f7f96dd919f9a4b31f5e9e964c
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: TextureModifier
  - hash:
      serializedVersion: 2
      Hash: 2f43860e745f8edade7bd381809e537b
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalAdditionalLightData
  - hash:
      serializedVersion: 2
      Hash: 2cc5af0f17cef9c717df88c8c7e4c9aa
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine
    className: LightAnchor
  - hash:
      serializedVersion: 2
      Hash: d0392b7f3f52d5af537ade186b4f51f1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlTrack
  - hash:
      serializedVersion: 2
      Hash: 3c05697026b5a8073c4c248c810547c4
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Data
    className: UnityTile
  - hash:
      serializedVersion: 2
      Hash: 7385c4e049f81d4276f731cb8a5bfb1d
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RegattaTVCamera
  - hash:
      serializedVersion: 2
      Hash: c7461ddb2926b22edcbffe17bf941824
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: DecalProjector
  - hash:
      serializedVersion: 2
      Hash: 32907f2a37539cbf6b4bf636831ef264
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveMSAA
  - hash:
      serializedVersion: 2
      Hash: a432ffa21c830dd55ff7e4b7099b67c8
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SerialReader
  - hash:
      serializedVersion: 2
      Hash: 67188d34689c9475455862bb822a1cc6
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptivePerformanceManagerSettings
  - hash:
      serializedVersion: 2
      Hash: 6c1ac2c4938eacb50bb9eccc2fde65d2
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: ModifierBase
  - hash:
      serializedVersion: 2
      Hash: 9a174471d4db2c0c6d3ac47d2f818db1
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnParticleCollisionMessageListener
  - hash:
      serializedVersion: 2
      Hash: 40be2ad159aa1a2b72ecc74cb38c7824
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: MultiplayerEventSystem
  - hash:
      serializedVersion: 2
      Hash: 2bbd05175d4cdd21e24f9716cdd24f83
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.XR
    className: TrackedPoseDriver
  - hash:
      serializedVersion: 2
      Hash: 0f5a086d4d028363983ba6ca30a01397
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseOverMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0237607a3a697cf130c195c13cb63a88
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: ColorGrading
  - hash:
      serializedVersion: 2
      Hash: 25719c5297e357f8477caf32e4ecf4a0
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RaceTimer
  - hash:
      serializedVersion: 2
      Hash: 24af424a437762b0c98a2238a41b2825
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerButton
  - hash:
      serializedVersion: 2
      Hash: 2c583ad0ac85d03e8700dd05fdcb46cb
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: Variables
  - hash:
      serializedVersion: 2
      Hash: 502a0d788634601faaefeacfda55e23a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DropdownSample
  - hash:
      serializedVersion: 2
      Hash: 9d9613c91f29a6f631f40f1e532fb7f6
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: CinemachineUniversalPixelPerfect
  - hash:
      serializedVersion: 2
      Hash: b30c8635462e66ac20f4241106119f77
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateMachine
  - hash:
      serializedVersion: 2
      Hash: c4de0ff7e075dd89c9fed5913dd775d2
    assemblyName: Assembly-CSharp
    namespaceName: TMPro
    className: TMP_PhoneNumberValidator
  - hash:
      serializedVersion: 2
      Hash: 3b8c10d6ace983dda3a813d2f6328297
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: TrackerSettings
  - hash:
      serializedVersion: 2
      Hash: fb91b0f531b5adecb53c856ab1c2f0aa
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: LensDistortion
  - hash:
      serializedVersion: 2
      Hash: c4ae0ec287bc3cde0bc98e1a65f9cef7
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexColorCycler
  - hash:
      serializedVersion: 2
      Hash: 7ec8b9099254520135a0697569b2d0a9
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRendererData
  - hash:
      serializedVersion: 2
      Hash: 1553f209ae0b2d5d3a35685fca55f9c3
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ScrollbarEventHandler
  - hash:
      serializedVersion: 2
      Hash: 42e3e08b69058a5d5eecf462b5845696
    assemblyName: Assembly-CSharp
    namespaceName: Bitgem.VFX.StylisedWater
    className: WateverVolumeFloater
  - hash:
      serializedVersion: 2
      Hash: 9eccc4340fcd994586e0dcb134c4612e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TeleType
  - hash:
      serializedVersion: 2
      Hash: 1362033cd491d66ad3cc206af29cc0de
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: Volume
  - hash:
      serializedVersion: 2
      Hash: 0deec24d8e56a34566d210d1c86655c4
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CircleRing
  - hash:
      serializedVersion: 2
      Hash: 9cb2893302356a2310c5dc3cfb630f31
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: LoadingPanelController
  - hash:
      serializedVersion: 2
      Hash: be240f9548bdc1c104b1401a3a70dd2f
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Interfaces
    className: LocationPrefabsLayerVisualizer
  - hash:
      serializedVersion: 2
      Hash: 360d3bc2983b155d4ce0473cf344b4e5
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXUISliderBinder
  - hash:
      serializedVersion: 2
      Hash: 082f1605c0df238c39b0e8250823bde6
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: ForwardGeocodeUserInput
  - hash:
      serializedVersion: 2
      Hash: 20be7214c278fde42443893ad9e61e21
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration
    className: KdTreeCollection
  - hash:
      serializedVersion: 2
      Hash: 628e7f629998c1439148da76ff71476e
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Map
    className: MapVisualizer
  - hash:
      serializedVersion: 2
      Hash: ff76cf6cf52b4db7b95af6ffadca9288
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalEmitter
  - hash:
      serializedVersion: 2
      Hash: 96e0a1b6d8ff4e0dbba4ea421d254f65
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RaceLineManager
  - hash:
      serializedVersion: 2
      Hash: c4cdb902228df56d5b2b639d6a6bbd3c
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: PlayerInputManager
  - hash:
      serializedVersion: 2
      Hash: 02a47340661d2ea7c6d30e292cfef403
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnToggleValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8bf800a764665b085fe469e8aea7f167
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 216f6c07bfa250e44b3e8d62572474e8
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark01
  - hash:
      serializedVersion: 2
      Hash: 4fcf0bb4aff1d14a1dfcdedc4b07bd05
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: TouchInputModule
  - hash:
      serializedVersion: 2
      Hash: 6b51fa0ff05a9e48b38bf430bb9d0ed8
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Location
    className: DeviceLocationProviderAndroidNative
  - hash:
      serializedVersion: 2
      Hash: 8c7c8e3f64ec57df74bde1c93ad900f0
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: StartSequenceTimer
  - hash:
      serializedVersion: 2
      Hash: 7b9dc5be0b7794a5629fe5e06f0fb068
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: FloatingBoatUI
  - hash:
      serializedVersion: 2
      Hash: 0814f85034902f3734f70fbf99b24c5c
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXRaycastBinder
  - hash:
      serializedVersion: 2
      Hash: 0f19765290d81f9e93eff7828a091d40
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: PlayerInput
  - hash:
      serializedVersion: 2
      Hash: 8d7ab1303b1c14da5fdd62bf96ca6e89
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: FreeCamera
  - hash:
      serializedVersion: 2
      Hash: 28be742dfed98d22c3acbf7df788a6b1
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveDecals
  - hash:
      serializedVersion: 2
      Hash: 34bcbd4b9a8d1ee94b65669777a5e8b4
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputButtonBinder
  - hash:
      serializedVersion: 2
      Hash: a3901985eb076c1c9b7562fca82ca3e0
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalAsset
  - hash:
      serializedVersion: 2
      Hash: 5d50072d64e42b475970b2b506897899
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Utilities
    className: DontDestroyOnLoad
  - hash:
      serializedVersion: 2
      Hash: 424074d3010ce4ec3d155fbdba52a3b3
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: Grain
  - hash:
      serializedVersion: 2
      Hash: c0199d88c3c460b6320e1794aee07597
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: PrefabModifier
  - hash:
      serializedVersion: 2
      Hash: 3586e7f4fd3cb20328f9458141273dab
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ScreenSpaceAmbientOcclusion
  - hash:
      serializedVersion: 2
      Hash: d0caa48b5fe6778ce3f0de7aec45c4e6
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: SkewTextExample
  - hash:
      serializedVersion: 2
      Hash: ed8e62f07ec878233f5493a335003493
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: VirtualMouseInput
  - hash:
      serializedVersion: 2
      Hash: 8703b6ae2b0eefd9dafbc44d8d333fb1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Shadow
  - hash:
      serializedVersion: 2
      Hash: f778e23b6ff26b8de4721be0eb1fa240
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: ba633961fffdd9ec3de055847db2685b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RankingRow
  - hash:
      serializedVersion: 2
      Hash: f854af586011dd194574f89546642e29
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 11a492830b189fafb9ed2f7afa464c56
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: SimpleScript
  - hash:
      serializedVersion: 2
      Hash: 822397528a45413ac77c5969482536f6
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SpectatorCameraController
  - hash:
      serializedVersion: 2
      Hash: 359a163207fb1c4f299e9a9533ca5f4e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UnityMainThreadDispatcher
  - hash:
      serializedVersion: 2
      Hash: 46903080eb6aa413dc57ea1a61692702
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: StartBuoyPosition
  - hash:
      serializedVersion: 2
      Hash: 14c6d608dff8177fa345174fc8cd09e9
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: MapboxStylesColorModifier
  - hash:
      serializedVersion: 2
      Hash: 75c6c9197c7a58bd598182947369a142
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Experimental.Rendering.Universal
    className: RenderObjects
  - hash:
      serializedVersion: 2
      Hash: b12640dfdb5966813e9dd838893e6c4a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0c6de72eaf8c1070da824268c4bfad9d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_StyleSheet
  - hash:
      serializedVersion: 2
      Hash: 01833f6af4ce9e780a4f97116326a6e7
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples.Scripts
    className: LogLocationProviderData
  - hash:
      serializedVersion: 2
      Hash: 1bc6ff8d02d2d68019aa6a028f8d409d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5cb38a9581158ac96efdf15faf56a040
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Location
    className: LocationProviderFactory
  - hash:
      serializedVersion: 2
      Hash: 7e93247d600e115dac0cdd880de94e99
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner
    className: PlaymodeTestsController
  - hash:
      serializedVersion: 2
      Hash: 83cfda82082c233d96cc677c19f57e07
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreakMessageListener
  - hash:
      serializedVersion: 2
      Hash: ea1f6cf689b020dbca6893f15c7e2607
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ResultsToggle
  - hash:
      serializedVersion: 2
      Hash: b72d9c057c0f9460abf04218f4cc4f0e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerMessageBox
  - hash:
      serializedVersion: 2
      Hash: 8f0b6cee07e52f58fdd615a13195d16d
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXVelocityBinder
  - hash:
      serializedVersion: 2
      Hash: d60a1ef5f486950f98a6c42372ee2166
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples.Voxels
    className: VoxelTile
  - hash:
      serializedVersion: 2
      Hash: 82be3cb603660f8df027a046fbbd582d
    assemblyName: Assembly-CSharp
    namespaceName: Bitgem.VFX.StylisedWater
    className: WaterVolumeTransforms
  - hash:
      serializedVersion: 2
      Hash: bcb41e6b94f2538f08663fc0a28a2bc2
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: FeatureSelectionDetector
  - hash:
      serializedVersion: 2
      Hash: 1ab136900c0edaeeb49424c852595030
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RawImage
  - hash:
      serializedVersion: 2
      Hash: 27fcf46f05da57849d306826fb32eb18
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: SplitToning
  - hash:
      serializedVersion: 2
      Hash: ff086b2fc4609f8b094e7c0435c6db0a
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: PostProcessLayer
  - hash:
      serializedVersion: 2
      Hash: 635b18b61811facfa20475ffe84ca1b3
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: PoiMarkerHelper
  - hash:
      serializedVersion: 2
      Hash: da281dc879c7932e0c9669da88ae40c0
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerGroup
  - hash:
      serializedVersion: 2
      Hash: 84056876ce8b14176cdd7f82aaefd21a
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXVisibilityEventBinder
  - hash:
      serializedVersion: 2
      Hash: 37dbaff32a49c70ff37b8bb15bd24c18
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: LensFlareComponentSRP
  - hash:
      serializedVersion: 2
      Hash: b6bda3a4fb33c2e1b24bbcc886adbc18
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ESP32GPSTracker
  - hash:
      serializedVersion: 2
      Hash: f3eb7e1d9604943aebe7540383c25b97
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Map
    className: InitializeMapWithLocationProvider
  - hash:
      serializedVersion: 2
      Hash: c9b1ea3bf987a2e780a24849503ca72e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark01_UGUI
  - hash:
      serializedVersion: 2
      Hash: e987953190eb4c12cf19df790566dc2c
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventSystem
  - hash:
      serializedVersion: 2
      Hash: 52db48e3be0a2b4f8b06f918fd1ebfbc
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Dropdown
  - hash:
      serializedVersion: 2
      Hash: f295412bc00b58b9095a71e9764cc886
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioTrack
  - hash:
      serializedVersion: 2
      Hash: ada873da50974d1e7ce247ffb296b0f3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: b15000e38a077c4ee9b2b796a99729b2
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Utilities.DebugTools
    className: SceneData
  - hash:
      serializedVersion: 2
      Hash: 49f757f74d62f1161997b55e6a137c5e
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: TrafficUvAnimator
  - hash:
      serializedVersion: 2
      Hash: 6ddf94363c6ce4b02d9fdd51290cb0f9
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestRunner.Utils
    className: TestRunCallbackListener
  - hash:
      serializedVersion: 2
      Hash: 5a4aa2850fe11587f47e34c270c7fd22
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: 3b5263a98bcc71d9a9d1d78a31b34ced
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SelectionCaret
  - hash:
      serializedVersion: 2
      Hash: 8e6dc3d79712ca4934f6d50c80f010b1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAnimator
  - hash:
      serializedVersion: 2
      Hash: e23d2210701e34e6850023cabe554c1f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldEndEditMessageListener
  - hash:
      serializedVersion: 2
      Hash: bf40c7a9dc15bc219687c9838aa9b41f
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: TestResultRendererCallback
  - hash:
      serializedVersion: 2
      Hash: aa325cf8525986b1f48a28230d9f514a
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ShadowCaster2D
  - hash:
      serializedVersion: 2
      Hash: 2139da8b88815e76622ff00868190166
    assemblyName: Assembly-CSharp
    namespaceName: Bitgem.VFX.StylisedWater
    className: WaterVolumeBase
  - hash:
      serializedVersion: 2
      Hash: c239bdb4eed8cef2b9f16829fadd1d61
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: AlertVisualEffect
  - hash:
      serializedVersion: 2
      Hash: a1ac4c50e55874de16eee3db71aa9784
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ToggleGroup
  - hash:
      serializedVersion: 2
      Hash: 593a641cd7822a10b9a54aa9eb5c3d5e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark04
  - hash:
      serializedVersion: 2
      Hash: 9f9cdf54e5244481fff61836419c3417
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples.Playground
    className: VectorTileExample
  - hash:
      serializedVersion: 2
      Hash: 1ff640df36b6706f21b65fcdd7fb30a7
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Utilities.DebugTools
    className: ScenesList
  - hash:
      serializedVersion: 2
      Hash: 7a0bde3ac6d739f92105cfe5613df2e0
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalAdditionalCameraData
  - hash:
      serializedVersion: 2
      Hash: b1fa2f301d48edb76d15f550ff95b0f2
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Factories
    className: TerrainFactoryBase
  - hash:
      serializedVersion: 2
      Hash: e9bfee93ebf3a7afacd9b5a36a892fc0
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: AutoExposure
  - hash:
      serializedVersion: 2
      Hash: d7c8092511063e0dae8106bfc8ed63dd
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollMessageListener
  - hash:
      serializedVersion: 2
      Hash: 715f7928b39881049ce8c46350cb8681
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformParentChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1534ee51e05e98ee8623a7ff6cd75ae9
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveShadowQuality
  - hash:
      serializedVersion: 2
      Hash: 86344100c716167fb86137c127ca9eee
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionImporterEditor
  - hash:
      serializedVersion: 2
      Hash: c6369b4cc3be620d6c9300e920bc3d52
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesAsset
  - hash:
      serializedVersion: 2
      Hash: 621da9214a2dcee2603df2d9663eab89
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: RotateWithLocationProvider
  - hash:
      serializedVersion: 2
      Hash: e240adf8daefdb8aea85a126a60c700f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: udp
  - hash:
      serializedVersion: 2
      Hash: 5e4e20849df822dc182b794cc80572be
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: TextureSideWallModifier
  - hash:
      serializedVersion: 2
      Hash: 1c8315ccd7d5aa95282635d3a91d4485
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: TeamInfoEditor
  - hash:
      serializedVersion: 2
      Hash: b810dfd26c061161a980f9a956f9a116
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ScreenSpaceShadows
  - hash:
      serializedVersion: 2
      Hash: 5b8b7230ad968399f6726aa1269dcdcb
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayerQuitHandler
  - hash:
      serializedVersion: 2
      Hash: d3bece132a2b48e63d78047dece48ba7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ChatController
  - hash:
      serializedVersion: 2
      Hash: cf32d98954e63b8e30e9334b152e9b8e
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: BaseInput
  - hash:
      serializedVersion: 2
      Hash: 162c5338c0a29efaeb270a4e55c07b62
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SoundManager
  - hash:
      serializedVersion: 2
      Hash: 49c636d0e90ec4e471c783d40edd72a4
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RegattaHelicopterCamera
  - hash:
      serializedVersion: 2
      Hash: e6f8333cc8a800e2866870a5ef7efc35
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnControllerColliderHitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 10ffd5579f941e0f4946756d18f1a9df
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerValueTuple
  - hash:
      serializedVersion: 2
      Hash: 91aa5a0bddee0a698a887f37e2d89629
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: DisableMeshRendererModifier
  - hash:
      serializedVersion: 2
      Hash: b462ce9b96d7f3f3a8a18557fbf44071
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: Bloom
  - hash:
      serializedVersion: 2
      Hash: be60f316c7805f2fafe2115ca4c7dafc
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ForwardRendererData
  - hash:
      serializedVersion: 2
      Hash: 8b3a3188bc2d2dd4890bda283efc1eb3
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: VectorDataFetcher
  - hash:
      serializedVersion: 2
      Hash: 2d85df95d201d9924deeebfe3c280ca0
    assemblyName: Unity.AdaptivePerformance.Google.Android
    namespaceName: UnityEngine.AdaptivePerformance.Google.Android
    className: GoogleAndroidProviderSettings
  - hash:
      serializedVersion: 2
      Hash: 5382156409d7c462c67822a5ee8d6c85
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggleHistory
  - hash:
      serializedVersion: 2
      Hash: c8ff8dd83d01bf95ee7f37c1190d4e1c
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: ReverseGeocodeUserInput
  - hash:
      serializedVersion: 2
      Hash: d5125db89c6b164bbac723664bbc5ee6
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptivePerformanceManager
  - hash:
      serializedVersion: 2
      Hash: 42284ecb06b05bfc619be3758fa5dd7a
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputActionAsset
  - hash:
      serializedVersion: 2
      Hash: 376bbfa6d185c54b052c7de579aaf797
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: ChangeShadowDistance
  - hash:
      serializedVersion: 2
      Hash: c9301dc19467b46389213a176105a156
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SceneChanger
  - hash:
      serializedVersion: 2
      Hash: bf95c68012d86df581ae079a39b15c7c
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Location
    className: DeviceLocationProvider
  - hash:
      serializedVersion: 2
      Hash: 3f7fd8381bb04913fb787b19a7ae65ad
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: IncrementStripIndexOnStart
  - hash:
      serializedVersion: 2
      Hash: 3b29be361a9cd56e98748b2001684c1e
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Data
    className: AtlasInfo
  - hash:
      serializedVersion: 2
      Hash: b0c8a1b11b1433071a20fd6e4c73a5f3
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveViewDistance
  - hash:
      serializedVersion: 2
      Hash: 665da785348a8806bf1524fe10742a60
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_UiFrameRateCounter
  - hash:
      serializedVersion: 2
      Hash: b747f5d40674055dd70e18552f1b0447
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Mask
  - hash:
      serializedVersion: 2
      Hash: 110aa327d5a5138dcfcbacb388ae0acc
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: PostProcessEffectSettings
  - hash:
      serializedVersion: 2
      Hash: 0754f5c2451c3c2da4411223cf081b1e
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: PositionWithLocationProvider
  - hash:
      serializedVersion: 2
      Hash: 164a37e0bc379311785dd54beac44331
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 5dc51b0140815ee83ba613dce32f5b12
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPersistentCanvas
  - hash:
      serializedVersion: 2
      Hash: 32bfadf4ab14c1e4298d945ac7dbee8f
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputStateWindow
  - hash:
      serializedVersion: 2
      Hash: 0bc7ef1b7516f21320ec49bfd31eef2e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: TrackedDeviceRaycaster
  - hash:
      serializedVersion: 2
      Hash: 4718916586b3818eb69bf65df61f0f3a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 185e7bacfbbc5defefa609c70f7ff4ce
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ColorGradient
  - hash:
      serializedVersion: 2
      Hash: 9dc274eab7228631e90d4757b13b768a
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ShadowsMidtonesHighlights
  - hash:
      serializedVersion: 2
      Hash: c78ed13f6b5a632d70d4f73f56ad81f2
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesSaver
  - hash:
      serializedVersion: 2
      Hash: 7053e305cdc1eb560f4c3be49d3b5136
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: PhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: a624e8926762e588c5e4bdc7ed77080f
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXHierarchyAttributeMapBinder
  - hash:
      serializedVersion: 2
      Hash: 042785b832afb73fd0585521aa7baf43
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 1a680d9a08de79b0d2c779e78c9843b4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RectMask2D
  - hash:
      serializedVersion: 2
      Hash: 8e34d163dd541ef79cfcd7c2aabb76a6
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: AppControl
  - hash:
      serializedVersion: 2
      Hash: 4ee1e5f285c826248e282a4df1635b19
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ChromaticAberration
  - hash:
      serializedVersion: 2
      Hash: 9ebc0a3016506a0cbb7e0306dfcb9497
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: DictionaryAsset
  - hash:
      serializedVersion: 2
      Hash: a151bdc57476c78f0cf6f66c9e18d5f2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BoatWaypointMover
  - hash:
      serializedVersion: 2
      Hash: 33b61e23005cc6ffa31471765ef59201
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: MaterialModifier
  - hash:
      serializedVersion: 2
      Hash: 3594d8e9fcbd74e51eec80efbf16c599
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: LensDistortion
  - hash:
      serializedVersion: 2
      Hash: 7d9543c1f1322bfae34b044b62676a76
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Renderer2DData
  - hash:
      serializedVersion: 2
      Hash: e7e95903ca66d58e1a7cbd71f824e16d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMeshUI
  - hash:
      serializedVersion: 2
      Hash: 0f6b6107168e347e854c0107aa8cb9fb
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumHistory
  - hash:
      serializedVersion: 2
      Hash: 8e15a5c833799867238b0bde425f9082
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CameraDirector
  - hash:
      serializedVersion: 2
      Hash: 890adc43cf1d7b01d544e0e93466362d
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXTransformBinder
  - hash:
      serializedVersion: 2
      Hash: 9078325c2a6f6a2d9612ade897511860
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAsset
  - hash:
      serializedVersion: 2
      Hash: 3e111a7a76f08b49956abfd1c233ccfd
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SimpleWindController
  - hash:
      serializedVersion: 2
      Hash: 9126d0be22d964159df51d6d53b879a9
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveTransparency
  - hash:
      serializedVersion: 2
      Hash: ea1b87a3bfed09792e7720338241c7a0
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDeviceDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 80a72b656b55a7a1fc92e1a02151ea22
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: ScreenSpaceReflections
  - hash:
      serializedVersion: 2
      Hash: ff71db1a48de41d3a8a5ffdfae1ea607
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputSystemObject
  - hash:
      serializedVersion: 2
      Hash: 05afd034bf7a074278a40ad9e97d8db4
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Vignette
  - hash:
      serializedVersion: 2
      Hash: 5a1710e1ce9ddf5eb06e0d2693171f49
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: ObjectSpin
  - hash:
      serializedVersion: 2
      Hash: fd42abf711cce7e443bdfcd187889929
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: SpawnInsideModifier
  - hash:
      serializedVersion: 2
      Hash: e559415f6893cccf0d196f78ca78b897
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: TagModifier
  - hash:
      serializedVersion: 2
      Hash: e1328794174ff9343524c2c7639c2242
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GPSWaypointDemo
  - hash:
      serializedVersion: 2
      Hash: f294bb42d237ff3f282e8652c9f7b475
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: SetSpawnTime
  - hash:
      serializedVersion: 2
      Hash: 0b63f6d3335e11939ce8790b904c8691
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: e402a382f213255c22bda9254dd831b3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: SceneVariables
  - hash:
      serializedVersion: 2
      Hash: ceeb6c51b89f022a3deb7b3408c66745
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_TextEventCheck
  - hash:
      serializedVersion: 2
      Hash: f2098fcd8ee983a05ec192f63904298f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnEndDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 76aa43cc6adae8454a66255a8113266d
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: AmbientOcclusion
  - hash:
      serializedVersion: 2
      Hash: 0bd2044f31f948e8e457585f08846860
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXTerrainBinder
  - hash:
      serializedVersion: 2
      Hash: 51b80d3734ea86b38e3101db98029e15
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 0370b9f95798139b666659c7e1be6147
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: BaseInputOverride
  - hash:
      serializedVersion: 2
      Hash: 47ee34eb8b6021efa0289dbbb17a5a85
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_InputField
  - hash:
      serializedVersion: 2
      Hash: ec6cf673540b2d4e42c612611eeafe4d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: ab3d19695db8289414a06e6d45fd6c8e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: ShaderPropAnimator
  - hash:
      serializedVersion: 2
      Hash: a86e3d6b5a1de39333b43511e6b081b3
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Components
    className: VertexDebugger
  - hash:
      serializedVersion: 2
      Hash: c058ff8faaff3e0da63b5433ebfb40bc
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Tonemapping
  - hash:
      serializedVersion: 2
      Hash: 8c3b6e24ca691fc0bf819c86e6b91dff
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Light2D
  - hash:
      serializedVersion: 2
      Hash: 96965dc53ba79d5861b5190a78d2900a
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: MergedModifierStack
  - hash:
      serializedVersion: 2
      Hash: f67f99d17ad2758ff1b7dcd208ce2f36
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: DragableDirectionWaypoint
  - hash:
      serializedVersion: 2
      Hash: 522f6865b4ec12522e602206785309f8
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: GlobalMessageListener
  - hash:
      serializedVersion: 2
      Hash: 53e329840e6d03aebec76623c3b054db
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Slider
  - hash:
      serializedVersion: 2
      Hash: 7d026ae582dc02c5edc3fc23b8c03ff7
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector3
  - hash:
      serializedVersion: 2
      Hash: 0c0b64d103fe555d830e8ca206abd12a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1981decccddfde3e97fcfd6689332a6f
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: WarpTextExample
  - hash:
      serializedVersion: 2
      Hash: 3645b97da6b9d55f246a57b0a5e2e8b7
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXLightBinder
  - hash:
      serializedVersion: 2
      Hash: 2183a96aa0442a0ba5fa82dd1b05cff4
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BoatInitializer
  - hash:
      serializedVersion: 2
      Hash: 17f82e3e7d8f8b07a270a4aad9bfe79d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_FontAsset
  - hash:
      serializedVersion: 2
      Hash: 4ea1bf258a208f00f70cf744bd7e56ce
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: Vignette
  - hash:
      serializedVersion: 2
      Hash: 3e21c4fc1790539a1a5e53c6dae8099a
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ChannelMixer
  - hash:
      serializedVersion: 2
      Hash: 63b9ef332f411e4eb4be1fe39fcecfd7
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Map.TileProviders
    className: RangeTileProvider
  - hash:
      serializedVersion: 2
      Hash: c20da6cc2dcf043bf9b4df4a492b2d95
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RegattaCameraSwitcher
  - hash:
      serializedVersion: 2
      Hash: 58785bcba46466317503951ef6e42079
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptivePerformanceGeneralSettings
  - hash:
      serializedVersion: 2
      Hash: b06ac189c77148892f458bf8f749bc61
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Utilities
    className: Console
  - hash:
      serializedVersion: 2
      Hash: 270df391cd455467ec523ee36f6e249e
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Utilities
    className: TelemetryConfigurationButton
  - hash:
      serializedVersion: 2
      Hash: 930f336a4b970a7d8c4c4438ab6a979c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ScriptablePalette
  - hash:
      serializedVersion: 2
      Hash: 73f3903e48b575a5c1466d0acfa985f9
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples.Playground
    className: ReverseGeocoderExample
  - hash:
      serializedVersion: 2
      Hash: d82086405f46de14829f6b4bcbdd8fc9
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameInvisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: 953fad5c5ab1e7bb889420c434db93fe
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFloatField
  - hash:
      serializedVersion: 2
      Hash: ee36d3b9c432504717ab224b3cb079bd
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Factories
    className: MapImageFactory
  - hash:
      serializedVersion: 2
      Hash: f99950819b81e9d497867d4286370d90
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SerialGPSTracker
  - hash:
      serializedVersion: 2
      Hash: bde1f4b1aa382adbb2cf77ac66710f2d
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GPSConfigurationManager
  - hash:
      serializedVersion: 2
      Hash: de74e1c2528065a2eee08fcc721d618b
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Location
    className: AngleSmoothingNoOp
  - hash:
      serializedVersion: 2
      Hash: 82834c11353dc668a39d726b6c9c267c
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Map.TileProviders
    className: QuadTreeTileProvider
  - hash:
      serializedVersion: 2
      Hash: 72461faee0001d571a9ff13b30a66aa9
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: TextMeshOutline
  - hash:
      serializedVersion: 2
      Hash: 2071f7ac454ae14eafecfd4041edae3a
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionEditorWindow
  - hash:
      serializedVersion: 2
      Hash: 1ce63d97d1ca25cba2d91d5f32fd5f79
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 6cf63e9dc888f92a3672d2e4db631c8e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputManagerEditor
  - hash:
      serializedVersion: 2
      Hash: d65655e8a5c1f5b413a6a24c4e996199
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: AddMonoBehavioursModifier
  - hash:
      serializedVersion: 2
      Hash: 049f6aa84fe1cb5359c9338e96d0b07e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: UIFoldout
  - hash:
      serializedVersion: 2
      Hash: 35e0b31791c377513b6fc7b9568c50cd
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark02
  - hash:
      serializedVersion: 2
      Hash: 74d802421531e1b911b106d5fe79e5a8
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: ObjectInspectorModifier
  - hash:
      serializedVersion: 2
      Hash: 2992c82698edefa6fb9f124b721c8bb9
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DataReceiver
  - hash:
      serializedVersion: 2
      Hash: 74a77ec7ff2ebcb0c1530e02ae38bd2f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ManualTeleportTrigger
  - hash:
      serializedVersion: 2
      Hash: 33656f83bb0079184b2206a69690ec46
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: AspectRatioFitter
  - hash:
      serializedVersion: 2
      Hash: f5c0512d18f84db81c10df70509b186b
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: ColliderModifier
  - hash:
      serializedVersion: 2
      Hash: c7d8343735fec59e3c49aee4f5c3278d
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CameraRotationController
  - hash:
      serializedVersion: 2
      Hash: b72839eb6e4350778dc879d98832aa2b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerHBox
  - hash:
      serializedVersion: 2
      Hash: 8f71b34e01767223badde86f9c585e87
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: AlertSystem
  - hash:
      serializedVersion: 2
      Hash: 8994247ddacb8f8856e38bfaae72d2fe
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveResolution
  - hash:
      serializedVersion: 2
      Hash: b7061b0ba437f16fb3eacf3e5b1b7564
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: SmoothLineModifier
  - hash:
      serializedVersion: 2
      Hash: 93a7afa748e4ef5b790dd5eeb3da558f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GPSWaypointSetup
  - hash:
      serializedVersion: 2
      Hash: da5a949a0bbd2ff91bc51c5805c2c41f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpAsButtonMessageListener
  - hash:
      serializedVersion: 2
      Hash: ef19274111491b6b8bcc6fd227f656f5
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectFloatField
  - hash:
      serializedVersion: 2
      Hash: fc0a9a681d29388386a9abf3f08b024a
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark03
  - hash:
      serializedVersion: 2
      Hash: ba331fc935a6a1327554a41b7ff164fe
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: VFXRuntimeResources
  - hash:
      serializedVersion: 2
      Hash: 428dc37a662797ebda61cf01a641f0e4
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: LineMeshModifier
  - hash:
      serializedVersion: 2
      Hash: 52675227776054b3678f01532fa1a6a8
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Map.TileProviders
    className: RangeAroundTransformTileProvider
  - hash:
      serializedVersion: 2
      Hash: 9104447b683bf70406fa01f12ecb564a
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: StandaloneInputModule
  - hash:
      serializedVersion: 2
      Hash: a90f6a00b23520788be7d89ee3b0773d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Scrollbar
  - hash:
      serializedVersion: 2
      Hash: febcfbf0a0d85e37808a3740e3c2c6fa
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: MarkerTrack
  - hash:
      serializedVersion: 2
      Hash: 316159625abf8f103839d6e8747de628
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: GameObjectModifier
  - hash:
      serializedVersion: 2
      Hash: ea5e0cb2f62c5bafc3dfb7bcdc9595e1
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: DebugUpdater
  - hash:
      serializedVersion: 2
      Hash: a908b098f2284663c0b9130e88844772
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerCanvas
  - hash:
      serializedVersion: 2
      Hash: 8ed5daadd237bb82e132790f75ffccaf
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputEditor
  - hash:
      serializedVersion: 2
      Hash: a92561298b80715aa69e8fa770123cb5
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: InputSystemUIInputModuleEditor
  - hash:
      serializedVersion: 2
      Hash: 8bbce9932f67ad8e7f4eefec87800f94
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextContainer
  - hash:
      serializedVersion: 2
      Hash: 0f77b4db266f398fef6fe3b62a5501c0
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: NoiseOffsetModifier
  - hash:
      serializedVersion: 2
      Hash: 67ba98f8a8977f3e60e5f0e049923033
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerColor
  - hash:
      serializedVersion: 2
      Hash: 3074afe1b03a0fb081e176a4ef1b9d09
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionAssetEditor
  - hash:
      serializedVersion: 2
      Hash: 7fdd3ee28e9042c3ec83463d0bb3b230
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_FrameRateCounter
  - hash:
      serializedVersion: 2
      Hash: f73e3a97805e1e671360c632c4565101
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: FilmGrain
  - hash:
      serializedVersion: 2
      Hash: 2a2eef56932c95206cbf2fb724fb40af
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXMultiplePositionBinder
  - hash:
      serializedVersion: 2
      Hash: 17142b03663387290480c82303274c02
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: f2305da2400dfe6f069770a1a63f0f7c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: PlayableTrack
  - hash:
      serializedVersion: 2
      Hash: 4392bfff79efe0630144f16ea4d656ab
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObjectList
  - hash:
      serializedVersion: 2
      Hash: c4b85a67513b4f0cf7b5fb2d5ded04b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 77cb6cd34fed7f292934dd99b70bbaed
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UIBoatSelection
  - hash:
      serializedVersion: 2
      Hash: 03a7ab9f91d257fdb3607e36c1d94e98
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Components
    className: TextureSelector
  - hash:
      serializedVersion: 2
      Hash: 7bb32d6e4c7614a97d9723ce7e26588c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: BasicPlayableBehaviour
  - hash:
      serializedVersion: 2
      Hash: 3af2769cab2514a1253ca02c88b6dd92
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Map
    className: AbstractMap
  - hash:
      serializedVersion: 2
      Hash: 19824dd35debb3fff0983df49f8359ab
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayModeRunnerCallback
  - hash:
      serializedVersion: 2
      Hash: 7e113c3458e08bd8c3dffc1966eb4743
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: ReloadMap
  - hash:
      serializedVersion: 2
      Hash: 6a0b5b415c00f783202fd1ae2d556cb2
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: b14e186823458f549289789cccbb77ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: 46f208ce9a71614152ba26df91af4e8d
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GPSDataFetcher
  - hash:
      serializedVersion: 2
      Hash: ee34255e3e86289bde9bad5e07b84cbc
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollRectValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 830c3b545f6e6ee60a4cebab90516f2c
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: RemoteTestResultSender
  - hash:
      serializedVersion: 2
      Hash: d0cdd713f9bf4e6a625d9e7804deca42
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventTrigger
  - hash:
      serializedVersion: 2
      Hash: 3c79a5e2dfba2d867c745cb494b76a36
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: QuadTreeCameraMovement
  - hash:
      serializedVersion: 2
      Hash: 52463c12db70bc60493608a25e33f934
    assemblyName: Assembly-CSharp
    namespaceName: Bitgem.VFX.StylisedWater
    className: WaterVolumeHelper
  - hash:
      serializedVersion: 2
      Hash: e2734bcc7b3e94776ade5d59f54fec1e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_TextSelector_B
  - hash:
      serializedVersion: 2
      Hash: b882f5188a5b7dc5dc8e9c1b29c24ae2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SliderTextUpdater
  - hash:
      serializedVersion: 2
      Hash: a6d4760c1aaaf3fc6e618d120234abd0
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: SpawnOverDistance
  - hash:
      serializedVersion: 2
      Hash: c674bbfb79e6bee5c0d725d89fd63649
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: ModifierStackBase
  - hash:
      serializedVersion: 2
      Hash: 67272c9c30c50b68ef1ce72ac0191c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMoveMessageListener
  - hash:
      serializedVersion: 2
      Hash: 326d8d2415bc1b7c1a648a865585bd35
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples.Playground
    className: RasterTileExample
  - hash:
      serializedVersion: 2
      Hash: 6dfbdb53722d39d4e316ef33be90973c
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: PostProcessData
  - hash:
      serializedVersion: 2
      Hash: c823d37b9a07b86172980929626974ef
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DirectGPSTeleporter
  - hash:
      serializedVersion: 2
      Hash: 2fd42819c69acefd80a2bbcd49cbe8cf
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: PostProcessProfile
  - hash:
      serializedVersion: 2
      Hash: dbe80c9dc024d18e75107a0b4da06133
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: InputSystemUIInputModule
  - hash:
      serializedVersion: 2
      Hash: 429ce0b58f9d2275bbc4ed740bee78ec
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: PostProcessDebug
  - hash:
      serializedVersion: 2
      Hash: b3eb5d73cd07eeee371bb1b136d026d6
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: 
    className: FullScreenPassRendererFeature
  - hash:
      serializedVersion: 2
      Hash: b4618c34232c33f1399e173d0ccd4982
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RaceController
  - hash:
      serializedVersion: 2
      Hash: 710ae1c9b325f59a36007abceb039638
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: AdaptiveLOD
  - hash:
      serializedVersion: 2
      Hash: eb0596742065f51f7812a64eb77d16c3
    assemblyName: Unity.AdaptivePerformance
    namespaceName: UnityEngine.AdaptivePerformance
    className: IAdaptivePerformanceSettings
  - hash:
      serializedVersion: 2
      Hash: be5dc5bc6fab622b269cf8e53738c1f6
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: TeamInfo
  - hash:
      serializedVersion: 2
      Hash: a8908e7b6d5f356b7151609c8bdc9fac
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SetCameraHeight
  - hash:
      serializedVersion: 2
      Hash: 3668c3ea69f8da8497b5d5b503d7d3c6
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BuoyPlacementUI
  - hash:
      serializedVersion: 2
      Hash: 01727a040aa965d7fae41c3c1e1d9ee5
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: PaniniProjection
  - hash:
      serializedVersion: 2
      Hash: debd75e86f6ad9e505030da81f8ca441
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: VisualEffectControlTrack
  - hash:
      serializedVersion: 2
      Hash: 000ce7938753e6991bf007db5c766ceb
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples.Voxels
    className: VoxelFetcher
  - hash:
      serializedVersion: 2
      Hash: 8f2f19715a4920dc92ce168d278d53e0
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Factories
    className: DirectionsFactory
  - hash:
      serializedVersion: 2
      Hash: e0489cb02bdeee1fc85b0d1a0dbcf838
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXEnabledBinder
  - hash:
      serializedVersion: 2
      Hash: 568292fb7ea6c4f7a667e5ff76bc7e85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: c133cbed746bddadc93cdcc5dfd2a11c
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: TextureMonoBehaviourModifier
  - hash:
      serializedVersion: 2
      Hash: 9fb1c3ab48b256d508ecddd967f98652
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexZoom
  - hash:
      serializedVersion: 2
      Hash: a2c8c180bbab1e962ff1cc307b089761
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.MeshGeneration.Modifiers
    className: ReplaceFeatureModifier
  - hash:
      serializedVersion: 2
      Hash: 711ba963e2b245a25de71beeaeeeb658
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFoldout
  - hash:
      serializedVersion: 2
      Hash: 3f74e77cf77a7f2aaf32d651c2baa032
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Location
    className: EditorLocationProvider
  - hash:
      serializedVersion: 2
      Hash: c2cafd9c11fa8198b82dacddc659cd45
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Location
    className: AngleSmoothingLowPass
  - hash:
      serializedVersion: 2
      Hash: 39277c468b3688935cf54fda26491426
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Unity.Map.TileProviders
    className: GlobeTileProvider
  - hash:
      serializedVersion: 2
      Hash: 06bbad1fcbaf4dd3a1bf03e93565183e
    assemblyName: Assembly-CSharp
    namespaceName: Mapbox.Examples
    className: HighlightBuildings
  - hash:
      serializedVersion: 2
      Hash: 8ed1980cbef176651b026ecb679ad294
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXRigidBodyCollisionEventBinder
  - hash:
      serializedVersion: 2
      Hash: 00eef92bf387da2c9009267123d9d674
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8b15e35eb98fc258ec2e839df7c3c9b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSliderValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 4bb8dde37a6865c0f9a229c9eb9a6cc9
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputAxisBinder
  - hash:
      serializedVersion: 2
      Hash: 3977e06696e10e422fe79adb7ace9f38
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ImageDataFetcher
  platform: 19
  scenePathNames:
  - Assets/Scenes/SplashScreen.unity
  - Assets/Scenes/RegateScene.unity
  - Assets/Scenes/Secu.unity
  playerPath: C:/PL/Projets Unity/RegattaVisionV5/Build/RegattaVision3D.exe
