{"BeeBuildProgramCommon.Data.ConfigurationData": {"Il2CppDir": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\il2cpp", "UnityLinkerPath": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\il2cpp/build/deploy/UnityLinker.exe", "Il2CppPath": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\il2cpp/build/deploy/il2cpp.exe", "NetCoreRunPath": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data\\Tools\\netcorerun\\netcorerun.exe", "DotNetExe": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/NetCoreRuntime/dotnet.exe", "EditorContentsPath": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data", "Packages": [{"Name": "com.unity.2d.sprite", "ResolvedPath": "Library/PackageCache/com.unity.2d.sprite@1.0.0"}, {"Name": "com.unity.adaptiveperformance", "ResolvedPath": "Library/PackageCache/com.unity.adaptiveperformance@5.1.2"}, {"Name": "com.unity.adaptiveperformance.google.android", "ResolvedPath": "Library/PackageCache/com.unity.adaptiveperformance.google.android@5.1.3"}, {"Name": "com.unity.collab-proxy", "ResolvedPath": "Library/PackageCache/com.unity.collab-proxy@2.6.0"}, {"Name": "com.unity.ide.rider", "ResolvedPath": "Library/PackageCache/com.unity.ide.rider@3.0.31"}, {"Name": "com.unity.ide.visualstudio", "ResolvedPath": "Library/PackageCache/com.unity.ide.visualstudio@2.0.22"}, {"Name": "com.unity.ide.vscode", "ResolvedPath": "Library/PackageCache/com.unity.ide.vscode@1.2.5"}, {"Name": "com.unity.inputsystem", "ResolvedPath": "Library/PackageCache/com.unity.inputsystem@1.7.0"}, {"Name": "com.unity.postprocessing", "ResolvedPath": "Library/PackageCache/com.unity.postprocessing@3.4.0"}, {"Name": "com.unity.render-pipelines.universal", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.universal@14.0.11"}, {"Name": "com.unity.shadergraph", "ResolvedPath": "Library/PackageCache/com.unity.shadergraph@14.0.11"}, {"Name": "com.unity.test-framework", "ResolvedPath": "Library/PackageCache/com.unity.test-framework@1.1.33"}, {"Name": "com.unity.textmeshpro", "ResolvedPath": "Library/PackageCache/com.unity.textmeshpro@3.0.9"}, {"Name": "com.unity.timeline", "ResolvedPath": "Library/PackageCache/com.unity.timeline@1.7.6"}, {"Name": "com.unity.ugui", "ResolvedPath": "Library/PackageCache/com.unity.ugui@1.0.0"}, {"Name": "com.unity.visualeffectgraph", "ResolvedPath": "Library/PackageCache/com.unity.visualeffectgraph@14.0.11"}, {"Name": "com.unity.visualscripting", "ResolvedPath": "Library/PackageCache/com.unity.visualscripting@1.9.4"}, {"Name": "com.unity.modules.ai", "ResolvedPath": "Library/PackageCache/com.unity.modules.ai@1.0.0"}, {"Name": "com.unity.modules.androidjni", "ResolvedPath": "Library/PackageCache/com.unity.modules.androidjni@1.0.0"}, {"Name": "com.unity.modules.animation", "ResolvedPath": "Library/PackageCache/com.unity.modules.animation@1.0.0"}, {"Name": "com.unity.modules.assetbundle", "ResolvedPath": "Library/PackageCache/com.unity.modules.assetbundle@1.0.0"}, {"Name": "com.unity.modules.audio", "ResolvedPath": "Library/PackageCache/com.unity.modules.audio@1.0.0"}, {"Name": "com.unity.modules.cloth", "ResolvedPath": "Library/PackageCache/com.unity.modules.cloth@1.0.0"}, {"Name": "com.unity.modules.director", "ResolvedPath": "Library/PackageCache/com.unity.modules.director@1.0.0"}, {"Name": "com.unity.modules.imageconversion", "ResolvedPath": "Library/PackageCache/com.unity.modules.imageconversion@1.0.0"}, {"Name": "com.unity.modules.imgui", "ResolvedPath": "Library/PackageCache/com.unity.modules.imgui@1.0.0"}, {"Name": "com.unity.modules.jsonserialize", "ResolvedPath": "Library/PackageCache/com.unity.modules.jsonserialize@1.0.0"}, {"Name": "com.unity.modules.particlesystem", "ResolvedPath": "Library/PackageCache/com.unity.modules.particlesystem@1.0.0"}, {"Name": "com.unity.modules.physics", "ResolvedPath": "Library/PackageCache/com.unity.modules.physics@1.0.0"}, {"Name": "com.unity.modules.physics2d", "ResolvedPath": "Library/PackageCache/com.unity.modules.physics2d@1.0.0"}, {"Name": "com.unity.modules.screencapture", "ResolvedPath": "Library/PackageCache/com.unity.modules.screencapture@1.0.0"}, {"Name": "com.unity.modules.terrain", "ResolvedPath": "Library/PackageCache/com.unity.modules.terrain@1.0.0"}, {"Name": "com.unity.modules.terrainphysics", "ResolvedPath": "Library/PackageCache/com.unity.modules.terrainphysics@1.0.0"}, {"Name": "com.unity.modules.tilemap", "ResolvedPath": "Library/PackageCache/com.unity.modules.tilemap@1.0.0"}, {"Name": "com.unity.modules.ui", "ResolvedPath": "Library/PackageCache/com.unity.modules.ui@1.0.0"}, {"Name": "com.unity.modules.uielements", "ResolvedPath": "Library/PackageCache/com.unity.modules.uielements@1.0.0"}, {"Name": "com.unity.modules.umbra", "ResolvedPath": "Library/PackageCache/com.unity.modules.umbra@1.0.0"}, {"Name": "com.unity.modules.unityanalytics", "ResolvedPath": "Library/PackageCache/com.unity.modules.unityanalytics@1.0.0"}, {"Name": "com.unity.modules.unitywebrequest", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequest@1.0.0"}, {"Name": "com.unity.modules.unitywebrequestassetbundle", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequestassetbundle@1.0.0"}, {"Name": "com.unity.modules.unitywebrequestaudio", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequestaudio@1.0.0"}, {"Name": "com.unity.modules.unitywebrequesttexture", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequesttexture@1.0.0"}, {"Name": "com.unity.modules.unitywebrequestwww", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequestwww@1.0.0"}, {"Name": "com.unity.modules.vehicles", "ResolvedPath": "Library/PackageCache/com.unity.modules.vehicles@1.0.0"}, {"Name": "com.unity.modules.video", "ResolvedPath": "Library/PackageCache/com.unity.modules.video@1.0.0"}, {"Name": "com.unity.modules.vr", "ResolvedPath": "Library/PackageCache/com.unity.modules.vr@1.0.0"}, {"Name": "com.unity.modules.wind", "ResolvedPath": "Library/PackageCache/com.unity.modules.wind@1.0.0"}, {"Name": "com.unity.modules.xr", "ResolvedPath": "Library/PackageCache/com.unity.modules.xr@1.0.0"}, {"Name": "com.unity.modules.subsystems", "ResolvedPath": "Library/PackageCache/com.unity.modules.subsystems@1.0.0"}, {"Name": "com.unity.render-pipelines.core", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.core@14.0.11"}, {"Name": "com.unity.ext.nunit", "ResolvedPath": "Library/PackageCache/com.unity.ext.nunit@1.0.6"}, {"Name": "com.unity.searcher", "ResolvedPath": "Library/PackageCache/com.unity.searcher@4.9.2"}, {"Name": "com.unity.mathematics", "ResolvedPath": "Library/PackageCache/com.unity.mathematics@1.2.6"}, {"Name": "com.unity.burst", "ResolvedPath": "Library/PackageCache/com.unity.burst@1.8.17"}, {"Name": "com.unity.render-pipelines.universal-config", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.universal-config@14.0.10"}, {"Name": "com.unity.profiling.core", "ResolvedPath": "Library/PackageCache/com.unity.profiling.core@1.0.2"}], "UnityVersion": "2022.3.47f1", "UnityVersionNumeric": {"Release": 2022, "Major": 3, "Minor": 47}, "AdvancedLicense": false, "Batchmode": false, "EmitDataForBeeWhy": false, "NamedPipeOrUnixSocket": "unity-ilpp-394cb04b78baec0acac68f6f41b0f4ea"}, "PlayerBuildProgramLibrary.Data.PlayerBuildConfig": {"DestinationPath": "Build/RegattaVision3D.exe", "StagingArea": "Temp/StagingArea", "DataFolder": "Library/PlayerDataCache/Win64/Data", "CompanyName": "Artyzia", "ProductName": "RegattaVision3D", "PlayerPackage": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport", "ApplicationIdentifier": "com.Unity-Technologies.com.unity.template.urp-blank", "Architecture": "x64", "UseIl2Cpp": false, "UseCoreCLR": false, "NoGUID": false, "InstallIntoBuildsFolder": false, "GenerateIdeProject": false, "Development": false, "UseNewInputSystem": true, "GenerateNativePluginsForAssembliesSettings": {"HasCallback": true, "DisplayName": "<PERSON> <PERSON><PERSON><PERSON>", "AdditionalInputFiles": ["ProjectSettings/BurstAotSettings_StandaloneWindows.json", "Library/BurstCache/AotSettings_StandaloneWindows.hash", "C:/PL/Projets Unity/RegattaVisionV5/Library/PackageCache/com.unity.burst@1.8.17/.Runtime\\bcl.exe"]}, "Services": {"EnableUnityConnect": false, "EnablePerformanceReporting": false, "EnableAnalytics": false, "EnableCrashReporting": false}, "ManagedAssemblies": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ARModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AccessibilityModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AndroidJNIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AnimationModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AudioModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClothModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterInputModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterRendererModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ContentLoadModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CoreModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CrashReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DSPGraphModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DirectorModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GameCenterModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GridModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HotReloadModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IMGUIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ImageConversionModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputLegacyModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.JSONSerializeModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.LocalizationModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.NVIDIAModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ParticleSystemModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PerformanceReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.Physics2DModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ProfilerModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PropertiesModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ScreenCaptureModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SharedInternalsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteMaskModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteShapeModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.StreamingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubstanceModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubsystemsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TLSModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainPhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreFontEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreTextEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextRenderingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TilemapModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIElementsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UmbraModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsCommonModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConnectModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityCurlModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityTestProtocolModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAudioModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestTextureModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestWWWModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VFXModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VRModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VehiclesModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VideoModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VirtualTexturingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.WindModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.XRModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Postprocessing.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.AdaptivePerformance.Google.Android.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Profiling.Core.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.AdaptivePerformance.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.Geometry.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.PbfReader.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/MapboxAccounts/net4x/MapboxAccountsUnity.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Plugins/M2Mqtt.Net.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/ThirdParty/Mapbox.Json/Net35/Mapbox.Json.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/ThirdParty/Mapbox.IO.Compression/net35/Mapbox.IO.Compression.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.VectorTileReader.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.ExtensionMethods.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/PackageCache/com.unity.burst@1.8.17/Unity.Burst.Unsafe.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/netstandard.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Security.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ComponentModel.Composition.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Configuration.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Core.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.DataSetExtensions.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Drawing.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.EnterpriseServices.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.FileSystem.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Numerics.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Serialization.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Security.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Internals.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Transactions.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.Linq.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/mscorlib.dll"], "StreamingAssetsFiles": []}, "PlayerBuildProgramLibrary.Data.PluginsData": {"Plugins": [{"AssetPath": "Assets/Mapbox/Core/Plugins/sqlite/x64/sqlite3.dll", "DestinationPath": "x86_64\\sqlite3.dll", "AddToEmbeddedBinaries": false}]}, "WinPlayerBuildProgram.Data.WinPlayerBuildConfig": {"VariationFolder": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono", "ServerPlayer": false, "CopyPdbFiles": false}}