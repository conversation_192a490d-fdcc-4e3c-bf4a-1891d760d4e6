{"Nodes": [{"Annotation": "all_tundra_nodes", "DisplayName": null, "Inputs": [], "Outputs": [], "ToBuildDependencies": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177], "OverwriteOutputs": false, "DebugActionIndex": 0}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/globalgamemanagers", "DisplayName": "Copying globalgamemanagers", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/globalgamemanagers"], "Outputs": ["Build/RegattaVision3D_Data/globalgamemanagers"], "DebugActionIndex": 1}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/globalgamemanagers.assets", "DisplayName": "Copying globalgamemanagers.assets", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/globalgamemanagers.assets"], "Outputs": ["Build/RegattaVision3D_Data/globalgamemanagers.assets"], "DebugActionIndex": 2}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/globalgamemanagers.assets.resS", "DisplayName": "Copying globalgamemanagers.assets.resS", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/globalgamemanagers.assets.resS"], "Outputs": ["Build/RegattaVision3D_Data/globalgamemanagers.assets.resS"], "DebugActionIndex": 3}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/level0", "DisplayName": "Copying level0", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/level0"], "Outputs": ["Build/RegattaVision3D_Data/level0"], "DebugActionIndex": 4}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/level0.resS", "DisplayName": "Copying level0.resS", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/level0.resS"], "Outputs": ["Build/RegattaVision3D_Data/level0.resS"], "DebugActionIndex": 5}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/level1", "DisplayName": "Copying level1", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/level1"], "Outputs": ["Build/RegattaVision3D_Data/level1"], "DebugActionIndex": 6}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/level1.resS", "DisplayName": "Copying level1.resS", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/level1.resS"], "Outputs": ["Build/RegattaVision3D_Data/level1.resS"], "DebugActionIndex": 7}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/level2", "DisplayName": "Copying level2", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/level2"], "Outputs": ["Build/RegattaVision3D_Data/level2"], "DebugActionIndex": 8}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/level2.resS", "DisplayName": "Copying level2.resS", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/level2.resS"], "Outputs": ["Build/RegattaVision3D_Data/level2.resS"], "DebugActionIndex": 9}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/resources.assets", "DisplayName": "Copying resources.assets", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/resources.assets"], "Outputs": ["Build/RegattaVision3D_Data/resources.assets"], "DebugActionIndex": 10}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/resources.assets.resS", "DisplayName": "Copying resources.assets.resS", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/resources.assets.resS"], "Outputs": ["Build/RegattaVision3D_Data/resources.assets.resS"], "DebugActionIndex": 11}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Resources/unity_builtin_extra", "DisplayName": "Copying unity_builtin_extra", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/Resources/unity_builtin_extra"], "Outputs": ["Build/RegattaVision3D_Data/Resources/unity_builtin_extra"], "DebugActionIndex": 12}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/RuntimeInitializeOnLoads.json", "DisplayName": "Copying RuntimeInitializeOnLoads.json", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/RuntimeInitializeOnLoads.json"], "Outputs": ["Build/RegattaVision3D_Data/RuntimeInitializeOnLoads.json"], "DebugActionIndex": 13}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/ScriptingAssemblies.json", "DisplayName": "Copying ScriptingAssemblies.json", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/ScriptingAssemblies.json"], "Outputs": ["Build/RegattaVision3D_Data/ScriptingAssemblies.json"], "DebugActionIndex": 14}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/sharedassets0.assets", "DisplayName": "Copying sharedassets0.assets", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/sharedassets0.assets"], "Outputs": ["Build/RegattaVision3D_Data/sharedassets0.assets"], "DebugActionIndex": 15}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/sharedassets0.assets.resS", "DisplayName": "Copying sharedassets0.assets.resS", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/sharedassets0.assets.resS"], "Outputs": ["Build/RegattaVision3D_Data/sharedassets0.assets.resS"], "DebugActionIndex": 16}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/sharedassets1.assets", "DisplayName": "Copying sharedassets1.assets", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/sharedassets1.assets"], "Outputs": ["Build/RegattaVision3D_Data/sharedassets1.assets"], "DebugActionIndex": 17}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/sharedassets1.assets.resS", "DisplayName": "Copying sharedassets1.assets.resS", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/sharedassets1.assets.resS"], "Outputs": ["Build/RegattaVision3D_Data/sharedassets1.assets.resS"], "DebugActionIndex": 18}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/sharedassets1.resource", "DisplayName": "Copying sharedassets1.resource", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/sharedassets1.resource"], "Outputs": ["Build/RegattaVision3D_Data/sharedassets1.resource"], "DebugActionIndex": 19}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/sharedassets2.assets", "DisplayName": "Copying sharedassets2.assets", "ActionType": "CopyFiles", "Inputs": ["Library/PlayerDataCache/Win64/Data/sharedassets2.assets"], "Outputs": ["Build/RegattaVision3D_Data/sharedassets2.assets"], "DebugActionIndex": 20}, {"Annotation": "WriteText Library/Bee/artifacts/csharpactions/boot.config_tyr4.info", "DisplayName": "Writing boot.config_tyr4.info", "ActionType": "WriteFile", "PayloadOffset": 83, "PayloadLength": 18879, "PayloadDebugContentSnippet": "{\"System.Object\":null,\"<PERSON>.Tun", "Inputs": [], "Outputs": ["Library/Bee/artifacts/csharpactions/boot.config_tyr4.info"], "DebugActionIndex": 21}, {"Annotation": "AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config", "DisplayName": "AddBootConfigGUID boot", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\WinPlayerBuildProgram.exe\" \"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Bee;C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline\" invoke_static_method \"Library\\Bee\\artifacts\\csharpactions\\boot.config_tyr4.info\"", "Inputs": ["Library/PlayerDataCache/Win64/Data/boot.config", "Library/PlayerDataCache/Win64/Data/boot.config", "Library/PlayerDataCache/Win64/Data/globalgamemanagers", "Library/PlayerDataCache/Win64/Data/globalgamemanagers.assets", "Library/PlayerDataCache/Win64/Data/globalgamemanagers.assets.resS", "Library/PlayerDataCache/Win64/Data/level0", "Library/PlayerDataCache/Win64/Data/level0.resS", "Library/PlayerDataCache/Win64/Data/level1", "Library/PlayerDataCache/Win64/Data/level1.resS", "Library/PlayerDataCache/Win64/Data/level2", "Library/PlayerDataCache/Win64/Data/level2.resS", "Library/PlayerDataCache/Win64/Data/resources.assets", "Library/PlayerDataCache/Win64/Data/resources.assets.resS", "Library/PlayerDataCache/Win64/Data/Resources/unity_builtin_extra", "Library/PlayerDataCache/Win64/Data/RuntimeInitializeOnLoads.json", "Library/PlayerDataCache/Win64/Data/ScriptingAssemblies.json", "Library/PlayerDataCache/Win64/Data/sharedassets0.assets", "Library/PlayerDataCache/Win64/Data/sharedassets0.assets.resS", "Library/PlayerDataCache/Win64/Data/sharedassets1.assets", "Library/PlayerDataCache/Win64/Data/sharedassets1.assets.resS", "Library/PlayerDataCache/Win64/Data/sharedassets1.resource", "Library/PlayerDataCache/Win64/Data/sharedassets2.assets", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ARModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AccessibilityModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AndroidJNIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AnimationModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AudioModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClothModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterInputModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterRendererModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ContentLoadModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CoreModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CrashReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DSPGraphModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DirectorModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GameCenterModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GridModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HotReloadModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IMGUIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ImageConversionModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputLegacyModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.JSONSerializeModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.LocalizationModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.NVIDIAModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ParticleSystemModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PerformanceReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.Physics2DModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ProfilerModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PropertiesModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ScreenCaptureModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SharedInternalsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteMaskModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteShapeModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.StreamingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubstanceModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubsystemsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TLSModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainPhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreFontEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreTextEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextRenderingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TilemapModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIElementsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UmbraModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsCommonModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConnectModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityCurlModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityTestProtocolModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAudioModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestTextureModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestWWWModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VFXModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VRModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VehiclesModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VideoModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VirtualTexturingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.WindModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.XRModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Postprocessing.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.AdaptivePerformance.Google.Android.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Profiling.Core.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.AdaptivePerformance.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.Geometry.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.PbfReader.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/MapboxAccounts/net4x/MapboxAccountsUnity.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Plugins/M2Mqtt.Net.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/ThirdParty/Mapbox.Json/Net35/Mapbox.Json.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/ThirdParty/Mapbox.IO.Compression/net35/Mapbox.IO.Compression.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.VectorTileReader.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.ExtensionMethods.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/PackageCache/com.unity.burst@1.8.17/Unity.Burst.Unsafe.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/netstandard.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Security.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ComponentModel.Composition.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Configuration.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Core.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.DataSetExtensions.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Drawing.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.EnterpriseServices.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.FileSystem.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Numerics.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Serialization.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Security.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Internals.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Transactions.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.Linq.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/mscorlib.dll", "Library/Bee/Playere2a68edc-inputdata.json", "Library/Bee/artifacts/csharpactions/boot.config_tyr4.info", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.BuildTools.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Core.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.CSharpSupport.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.DotNet.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.NativeProgramSupport.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Stevedore.Program.exe", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.TinyProfiler2.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.GNU.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.LLVM.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.VisualStudio.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.Xcode.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Tools.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.TundraBackend.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.VisualStudioSolution.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/BeeBuildProgramCommon.Data.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/BeeBuildProgramCommon.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/BeeLocalCacheTool.exe", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Newtonsoft.Json.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/NiceIO.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/PlayerBuildProgramLibrary.Data.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/ScriptCompilationBuildProgram.Data.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/ScriptCompilationBuildProgram.exe", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/SharpYaml.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Api.Attributes.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Mdb.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Pdb.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Rocks.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Api.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Bee.BuildLogic.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Linker.Api.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Options.dll"], "Outputs": ["Library/Bee/artifacts/WinPlayerBuildProgram/boot.config"], "ToBuildDependencies": [21], "OverwriteOutputs": false, "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 22}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/boot.config", "DisplayName": "Copying boot.config", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/WinPlayerBuildProgram/boot.config"], "Outputs": ["Build/RegattaVision3D_Data/boot.config"], "ToBuildDependencies": [22], "DebugActionIndex": 23}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/StreamingAssets/meshtastic_gps_tracker.py", "DisplayName": "Copying meshtastic_gps_tracker.py", "ActionType": "CopyFiles", "Inputs": ["Assets/StreamingAssets/meshtastic_gps_tracker.py"], "Outputs": ["Build/RegattaVision3D_Data/StreamingAssets/meshtastic_gps_tracker.py"], "DebugActionIndex": 24}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Plugins/x86_64/sqlite3.dll", "DisplayName": "Copying sqlite3.dll", "ActionType": "CopyFiles", "Inputs": ["Assets/Mapbox/Core/Plugins/sqlite/x64/sqlite3.dll"], "Outputs": ["Build/RegattaVision3D_Data/Plugins/x86_64/sqlite3.dll"], "DebugActionIndex": 25}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.AIModule.dll", "DisplayName": "Copying UnityEngine.AIModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AIModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.AIModule.dll"], "DebugActionIndex": 26}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.ARModule.dll", "DisplayName": "Copying UnityEngine.ARModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ARModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.ARModule.dll"], "DebugActionIndex": 27}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.AccessibilityModule.dll", "DisplayName": "Copying UnityEngine.AccessibilityModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AccessibilityModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.AccessibilityModule.dll"], "DebugActionIndex": 28}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.AndroidJNIModule.dll", "DisplayName": "Copying UnityEngine.AndroidJNIModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AndroidJNIModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.AndroidJNIModule.dll"], "DebugActionIndex": 29}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.AnimationModule.dll", "DisplayName": "Copying UnityEngine.AnimationModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AnimationModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.AnimationModule.dll"], "DebugActionIndex": 30}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.AssetBundleModule.dll", "DisplayName": "Copying UnityEngine.AssetBundleModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AssetBundleModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.AssetBundleModule.dll"], "DebugActionIndex": 31}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.AudioModule.dll", "DisplayName": "Copying UnityEngine.AudioModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AudioModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.AudioModule.dll"], "DebugActionIndex": 32}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.ClothModule.dll", "DisplayName": "Copying UnityEngine.ClothModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClothModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.ClothModule.dll"], "DebugActionIndex": 33}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.ClusterInputModule.dll", "DisplayName": "Copying UnityEngine.ClusterInputModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterInputModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.ClusterInputModule.dll"], "DebugActionIndex": 34}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.ClusterRendererModule.dll", "DisplayName": "Copying UnityEngine.ClusterRendererModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterRendererModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.ClusterRendererModule.dll"], "DebugActionIndex": 35}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.ContentLoadModule.dll", "DisplayName": "Copying UnityEngine.ContentLoadModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ContentLoadModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.ContentLoadModule.dll"], "DebugActionIndex": 36}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.CoreModule.dll", "DisplayName": "Copying UnityEngine.CoreModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CoreModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.CoreModule.dll"], "DebugActionIndex": 37}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.CrashReportingModule.dll", "DisplayName": "Copying UnityEngine.CrashReportingModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CrashReportingModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.CrashReportingModule.dll"], "DebugActionIndex": 38}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.DSPGraphModule.dll", "DisplayName": "Copying UnityEngine.DSPGraphModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DSPGraphModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.DSPGraphModule.dll"], "DebugActionIndex": 39}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.DirectorModule.dll", "DisplayName": "Copying UnityEngine.DirectorModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DirectorModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.DirectorModule.dll"], "DebugActionIndex": 40}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.GIModule.dll", "DisplayName": "Copying UnityEngine.GIModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GIModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.GIModule.dll"], "DebugActionIndex": 41}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.GameCenterModule.dll", "DisplayName": "Copying UnityEngine.GameCenterModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GameCenterModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.GameCenterModule.dll"], "DebugActionIndex": 42}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.GridModule.dll", "DisplayName": "Copying UnityEngine.GridModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GridModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.GridModule.dll"], "DebugActionIndex": 43}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.HotReloadModule.dll", "DisplayName": "Copying UnityEngine.HotReloadModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HotReloadModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.HotReloadModule.dll"], "DebugActionIndex": 44}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.IMGUIModule.dll", "DisplayName": "Copying UnityEngine.IMGUIModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IMGUIModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.IMGUIModule.dll"], "DebugActionIndex": 45}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.ImageConversionModule.dll", "DisplayName": "Copying UnityEngine.ImageConversionModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ImageConversionModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.ImageConversionModule.dll"], "DebugActionIndex": 46}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.InputLegacyModule.dll", "DisplayName": "Copying UnityEngine.InputLegacyModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputLegacyModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.InputLegacyModule.dll"], "DebugActionIndex": 47}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.InputModule.dll", "DisplayName": "Copying UnityEngine.InputModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.InputModule.dll"], "DebugActionIndex": 48}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.JSONSerializeModule.dll", "DisplayName": "Copying UnityEngine.JSONSerializeModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.JSONSerializeModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.JSONSerializeModule.dll"], "DebugActionIndex": 49}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.LocalizationModule.dll", "DisplayName": "Copying UnityEngine.LocalizationModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.LocalizationModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.LocalizationModule.dll"], "DebugActionIndex": 50}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.NVIDIAModule.dll", "DisplayName": "Copying UnityEngine.NVIDIAModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.NVIDIAModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.NVIDIAModule.dll"], "DebugActionIndex": 51}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.ParticleSystemModule.dll", "DisplayName": "Copying UnityEngine.ParticleSystemModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ParticleSystemModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.ParticleSystemModule.dll"], "DebugActionIndex": 52}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.PerformanceReportingModule.dll", "DisplayName": "Copying UnityEngine.PerformanceReportingModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PerformanceReportingModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.PerformanceReportingModule.dll"], "DebugActionIndex": 53}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.Physics2DModule.dll", "DisplayName": "Copying UnityEngine.Physics2DModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.Physics2DModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.Physics2DModule.dll"], "DebugActionIndex": 54}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.PhysicsModule.dll", "DisplayName": "Copying UnityEngine.PhysicsModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PhysicsModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.PhysicsModule.dll"], "DebugActionIndex": 55}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.ProfilerModule.dll", "DisplayName": "Copying UnityEngine.ProfilerModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ProfilerModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.ProfilerModule.dll"], "DebugActionIndex": 56}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.PropertiesModule.dll", "DisplayName": "Copying UnityEngine.PropertiesModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PropertiesModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.PropertiesModule.dll"], "DebugActionIndex": 57}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "DisplayName": "Copying UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"], "DebugActionIndex": 58}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.ScreenCaptureModule.dll", "DisplayName": "Copying UnityEngine.ScreenCaptureModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ScreenCaptureModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.ScreenCaptureModule.dll"], "DebugActionIndex": 59}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.SharedInternalsModule.dll", "DisplayName": "Copying UnityEngine.SharedInternalsModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SharedInternalsModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.SharedInternalsModule.dll"], "DebugActionIndex": 60}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.SpriteMaskModule.dll", "DisplayName": "Copying UnityEngine.SpriteMaskModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteMaskModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.SpriteMaskModule.dll"], "DebugActionIndex": 61}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.SpriteShapeModule.dll", "DisplayName": "Copying UnityEngine.SpriteShapeModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteShapeModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.SpriteShapeModule.dll"], "DebugActionIndex": 62}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.StreamingModule.dll", "DisplayName": "Copying UnityEngine.StreamingModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.StreamingModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.StreamingModule.dll"], "DebugActionIndex": 63}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.SubstanceModule.dll", "DisplayName": "Copying UnityEngine.SubstanceModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubstanceModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.SubstanceModule.dll"], "DebugActionIndex": 64}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.SubsystemsModule.dll", "DisplayName": "Copying UnityEngine.SubsystemsModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubsystemsModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.SubsystemsModule.dll"], "DebugActionIndex": 65}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.TLSModule.dll", "DisplayName": "Copying UnityEngine.TLSModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TLSModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.TLSModule.dll"], "DebugActionIndex": 66}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.TerrainModule.dll", "DisplayName": "Copying UnityEngine.TerrainModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.TerrainModule.dll"], "DebugActionIndex": 67}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.TerrainPhysicsModule.dll", "DisplayName": "Copying UnityEngine.TerrainPhysicsModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainPhysicsModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.TerrainPhysicsModule.dll"], "DebugActionIndex": 68}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.TextCoreFontEngineModule.dll", "DisplayName": "Copying UnityEngine.TextCoreFontEngineModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreFontEngineModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.TextCoreFontEngineModule.dll"], "DebugActionIndex": 69}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.TextCoreTextEngineModule.dll", "DisplayName": "Copying UnityEngine.TextCoreTextEngineModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreTextEngineModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.TextCoreTextEngineModule.dll"], "DebugActionIndex": 70}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.TextRenderingModule.dll", "DisplayName": "Copying UnityEngine.TextRenderingModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextRenderingModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.TextRenderingModule.dll"], "DebugActionIndex": 71}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.TilemapModule.dll", "DisplayName": "Copying UnityEngine.TilemapModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TilemapModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.TilemapModule.dll"], "DebugActionIndex": 72}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UIElementsModule.dll", "DisplayName": "Copying UnityEngine.UIElementsModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIElementsModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UIElementsModule.dll"], "DebugActionIndex": 73}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UIModule.dll", "DisplayName": "Copying UnityEngine.UIModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UIModule.dll"], "DebugActionIndex": 74}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UmbraModule.dll", "DisplayName": "Copying UnityEngine.UmbraModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UmbraModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UmbraModule.dll"], "DebugActionIndex": 75}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UnityAnalyticsCommonModule.dll", "DisplayName": "Copying UnityEngine.UnityAnalyticsCommonModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsCommonModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UnityAnalyticsCommonModule.dll"], "DebugActionIndex": 76}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UnityAnalyticsModule.dll", "DisplayName": "Copying UnityEngine.UnityAnalyticsModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UnityAnalyticsModule.dll"], "DebugActionIndex": 77}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UnityConnectModule.dll", "DisplayName": "Copying UnityEngine.UnityConnectModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConnectModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UnityConnectModule.dll"], "DebugActionIndex": 78}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UnityCurlModule.dll", "DisplayName": "Copying UnityEngine.UnityCurlModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityCurlModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UnityCurlModule.dll"], "DebugActionIndex": 79}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UnityTestProtocolModule.dll", "DisplayName": "Copying UnityEngine.UnityTestProtocolModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityTestProtocolModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UnityTestProtocolModule.dll"], "DebugActionIndex": 80}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll", "DisplayName": "Copying UnityEngine.UnityWebRequestAssetBundleModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll"], "DebugActionIndex": 81}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll", "DisplayName": "Copying UnityEngine.UnityWebRequestAudioModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAudioModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll"], "DebugActionIndex": 82}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestModule.dll", "DisplayName": "Copying UnityEngine.UnityWebRequestModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestModule.dll"], "DebugActionIndex": 83}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestTextureModule.dll", "DisplayName": "Copying UnityEngine.UnityWebRequestTextureModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestTextureModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestTextureModule.dll"], "DebugActionIndex": 84}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestWWWModule.dll", "DisplayName": "Copying UnityEngine.UnityWebRequestWWWModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestWWWModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UnityWebRequestWWWModule.dll"], "DebugActionIndex": 85}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.VFXModule.dll", "DisplayName": "Copying UnityEngine.VFXModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VFXModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.VFXModule.dll"], "DebugActionIndex": 86}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.VRModule.dll", "DisplayName": "Copying UnityEngine.VRModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VRModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.VRModule.dll"], "DebugActionIndex": 87}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.VehiclesModule.dll", "DisplayName": "Copying UnityEngine.VehiclesModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VehiclesModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.VehiclesModule.dll"], "DebugActionIndex": 88}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.VideoModule.dll", "DisplayName": "Copying UnityEngine.VideoModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VideoModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.VideoModule.dll"], "DebugActionIndex": 89}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.VirtualTexturingModule.dll", "DisplayName": "Copying UnityEngine.VirtualTexturingModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VirtualTexturingModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.VirtualTexturingModule.dll"], "DebugActionIndex": 90}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.WindModule.dll", "DisplayName": "Copying UnityEngine.WindModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.WindModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.WindModule.dll"], "DebugActionIndex": 91}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.XRModule.dll", "DisplayName": "Copying UnityEngine.XRModule.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.XRModule.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.XRModule.dll"], "DebugActionIndex": 92}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.dll", "DisplayName": "Copying UnityEngine.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.dll"], "DebugActionIndex": 93}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Assembly-CSharp.dll", "DisplayName": "Copying Assembly-CSharp.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Assembly-CSharp.dll"], "DebugActionIndex": 94}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.VisualEffectGraph.Runtime.dll", "DisplayName": "Copying Unity.VisualEffectGraph.Runtime.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.VisualEffectGraph.Runtime.dll"], "DebugActionIndex": 95}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Core.Runtime.dll", "DisplayName": "Copying Unity.RenderPipelines.Core.Runtime.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Core.Runtime.dll"], "DebugActionIndex": 96}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.Postprocessing.Runtime.dll", "DisplayName": "Copying Unity.Postprocessing.Runtime.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Postprocessing.Runtime.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.Postprocessing.Runtime.dll"], "DebugActionIndex": 97}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Universal.Config.Runtime.dll", "DisplayName": "Copying Unity.RenderPipelines.Universal.Config.Runtime.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Universal.Config.Runtime.dll"], "DebugActionIndex": 98}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.VisualScripting.Flow.dll", "DisplayName": "Copying Unity.VisualScripting.Flow.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.VisualScripting.Flow.dll"], "DebugActionIndex": 99}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Core.ShaderLibrary.dll", "DisplayName": "Copying Unity.RenderPipelines.Core.ShaderLibrary.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Core.ShaderLibrary.dll"], "DebugActionIndex": 100}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.AdaptivePerformance.Google.Android.dll", "DisplayName": "Copying Unity.AdaptivePerformance.Google.Android.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.AdaptivePerformance.Google.Android.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.AdaptivePerformance.Google.Android.dll"], "DebugActionIndex": 101}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.TextMeshPro.dll", "DisplayName": "Copying Unity.TextMeshPro.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.TextMeshPro.dll"], "DebugActionIndex": 102}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.Profiling.Core.dll", "DisplayName": "Copying Unity.Profiling.Core.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Profiling.Core.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.Profiling.Core.dll"], "DebugActionIndex": 103}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "DisplayName": "Copying Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"], "DebugActionIndex": 104}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Universal.Runtime.dll", "DisplayName": "Copying Unity.RenderPipelines.Universal.Runtime.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Universal.Runtime.dll"], "DebugActionIndex": 105}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.Burst.dll", "DisplayName": "Copying Unity.Burst.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.Burst.dll"], "DebugActionIndex": 106}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.AdaptivePerformance.dll", "DisplayName": "Copying Unity.AdaptivePerformance.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.AdaptivePerformance.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.AdaptivePerformance.dll"], "DebugActionIndex": 107}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.VisualScripting.Core.dll", "DisplayName": "Copying Unity.VisualScripting.Core.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.VisualScripting.Core.dll"], "DebugActionIndex": 108}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/UnityEngine.UI.dll", "DisplayName": "Copying UnityEngine.UI.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/UnityEngine.UI.dll"], "DebugActionIndex": 109}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "DisplayName": "Copying Unity.RenderPipeline.Universal.ShaderLibrary.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.RenderPipeline.Universal.ShaderLibrary.dll"], "DebugActionIndex": 110}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.Timeline.dll", "DisplayName": "Copying Unity.Timeline.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.Timeline.dll"], "DebugActionIndex": 111}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.InputSystem.dll", "DisplayName": "Copying Unity.InputSystem.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.InputSystem.dll"], "DebugActionIndex": 112}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.InputSystem.ForUI.dll", "DisplayName": "Copying Unity.InputSystem.ForUI.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.InputSystem.ForUI.dll"], "DebugActionIndex": 113}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.Mathematics.dll", "DisplayName": "Copying Unity.Mathematics.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.Mathematics.dll"], "DebugActionIndex": 114}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Universal.Shaders.dll", "DisplayName": "Copying Unity.RenderPipelines.Universal.Shaders.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.RenderPipelines.Universal.Shaders.dll"], "DebugActionIndex": 115}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.VisualScripting.State.dll", "DisplayName": "Copying Unity.VisualScripting.State.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.VisualScripting.State.dll"], "DebugActionIndex": 116}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Mapbox.VectorTile.Geometry.dll", "DisplayName": "Copying Mapbox.VectorTile.Geometry.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.Geometry.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Mapbox.VectorTile.Geometry.dll"], "DebugActionIndex": 117}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Mapbox.VectorTile.PbfReader.dll", "DisplayName": "Copying Mapbox.VectorTile.PbfReader.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.PbfReader.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Mapbox.VectorTile.PbfReader.dll"], "DebugActionIndex": 118}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/MapboxAccountsUnity.dll", "DisplayName": "Copying MapboxAccountsUnity.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/MapboxAccounts/net4x/MapboxAccountsUnity.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/MapboxAccountsUnity.dll"], "DebugActionIndex": 119}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/M2Mqtt.Net.dll", "DisplayName": "Copying M2Mqtt.Net.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Assets/Plugins/M2Mqtt.Net.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/M2Mqtt.Net.dll"], "DebugActionIndex": 120}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Mapbox.Json.dll", "DisplayName": "Copying Mapbox.Json.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/ThirdParty/Mapbox.Json/Net35/Mapbox.Json.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Mapbox.Json.dll"], "DebugActionIndex": 121}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Mapbox.IO.Compression.dll", "DisplayName": "Copying Mapbox.IO.Compression.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/ThirdParty/Mapbox.IO.Compression/net35/Mapbox.IO.Compression.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Mapbox.IO.Compression.dll"], "DebugActionIndex": 122}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Mapbox.VectorTile.VectorTileReader.dll", "DisplayName": "Copying Mapbox.VectorTile.VectorTileReader.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.VectorTileReader.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Mapbox.VectorTile.VectorTileReader.dll"], "DebugActionIndex": 123}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Mapbox.VectorTile.ExtensionMethods.dll", "DisplayName": "Copying Mapbox.VectorTile.ExtensionMethods.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.ExtensionMethods.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Mapbox.VectorTile.ExtensionMethods.dll"], "DebugActionIndex": 124}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.VisualScripting.Antlr3.Runtime.dll", "DisplayName": "Copying Unity.VisualScripting.Antlr3.Runtime.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.VisualScripting.Antlr3.Runtime.dll"], "DebugActionIndex": 125}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Unity.Burst.Unsafe.dll", "DisplayName": "Copying Unity.Burst.Unsafe.dll", "ActionType": "CopyFiles", "Inputs": ["C:/PL/Projets Unity/RegattaVisionV5/Library/PackageCache/com.unity.burst@1.8.17/Unity.Burst.Unsafe.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Unity.Burst.Unsafe.dll"], "DebugActionIndex": 126}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Runtime.dll", "DisplayName": "Copying System.Runtime.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Runtime.dll"], "DebugActionIndex": 127}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/netstandard.dll", "DisplayName": "Copying netstandard.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/netstandard.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/netstandard.dll"], "DebugActionIndex": 128}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/Mono.Security.dll", "DisplayName": "Copying Mono.Security.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Security.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/Mono.Security.dll"], "DebugActionIndex": 129}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.ComponentModel.Composition.dll", "DisplayName": "Copying System.ComponentModel.Composition.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ComponentModel.Composition.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.ComponentModel.Composition.dll"], "DebugActionIndex": 130}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Configuration.dll", "DisplayName": "Copying System.Configuration.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Configuration.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Configuration.dll"], "DebugActionIndex": 131}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Core.dll", "DisplayName": "Copying System.Core.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Core.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Core.dll"], "DebugActionIndex": 132}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Data.DataSetExtensions.dll", "DisplayName": "Copying System.Data.DataSetExtensions.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.DataSetExtensions.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Data.DataSetExtensions.dll"], "DebugActionIndex": 133}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Data.dll", "DisplayName": "Copying System.Data.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Data.dll"], "DebugActionIndex": 134}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Drawing.dll", "DisplayName": "Copying System.Drawing.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Drawing.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Drawing.dll"], "DebugActionIndex": 135}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.EnterpriseServices.dll", "DisplayName": "Copying System.EnterpriseServices.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.EnterpriseServices.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.EnterpriseServices.dll"], "DebugActionIndex": 136}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.IO.Compression.FileSystem.dll", "DisplayName": "Copying System.IO.Compression.FileSystem.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.FileSystem.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.IO.Compression.FileSystem.dll"], "DebugActionIndex": 137}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.IO.Compression.dll", "DisplayName": "Copying System.IO.Compression.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.IO.Compression.dll"], "DebugActionIndex": 138}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Net.Http.dll", "DisplayName": "Copying System.Net.Http.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Net.Http.dll"], "DebugActionIndex": 139}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Numerics.dll", "DisplayName": "Copying System.Numerics.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Numerics.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Numerics.dll"], "DebugActionIndex": 140}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Runtime.Serialization.dll", "DisplayName": "Copying System.Runtime.Serialization.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Serialization.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Runtime.Serialization.dll"], "DebugActionIndex": 141}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Security.dll", "DisplayName": "Copying System.Security.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Security.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Security.dll"], "DebugActionIndex": 142}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.ServiceModel.Internals.dll", "DisplayName": "Copying System.ServiceModel.Internals.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Internals.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.ServiceModel.Internals.dll"], "DebugActionIndex": 143}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Transactions.dll", "DisplayName": "Copying System.Transactions.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Transactions.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Transactions.dll"], "DebugActionIndex": 144}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Xml.Linq.dll", "DisplayName": "Copying System.Xml.Linq.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.Linq.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Xml.Linq.dll"], "DebugActionIndex": 145}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.Xml.dll", "DisplayName": "Copying System.Xml.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.Xml.dll"], "DebugActionIndex": 146}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/System.dll", "DisplayName": "Copying System.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/System.dll"], "DebugActionIndex": 147}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Managed/mscorlib.dll", "DisplayName": "Copying mscorlib.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/mscorlib.dll"], "Outputs": ["Build/RegattaVision3D_Data/Managed/mscorlib.dll"], "DebugActionIndex": 148}, {"Annotation": "GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker", "DisplayName": "<PERSON> <PERSON><PERSON><PERSON>", "ActionType": "RunRPCCommand", "PayloadOffset": 19085, "PayloadLength": 17455, "PayloadDebugContentSnippet": "{\"PlayerBuildProgramLibrary.Da", "Action": "GenerateNativePluginsForAssemblies", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ARModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AccessibilityModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AndroidJNIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AnimationModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AudioModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClothModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterInputModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterRendererModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ContentLoadModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CoreModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CrashReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DSPGraphModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DirectorModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GameCenterModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GridModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HotReloadModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IMGUIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ImageConversionModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputLegacyModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.JSONSerializeModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.LocalizationModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.NVIDIAModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ParticleSystemModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PerformanceReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.Physics2DModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ProfilerModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PropertiesModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ScreenCaptureModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SharedInternalsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteMaskModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteShapeModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.StreamingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubstanceModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubsystemsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TLSModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainPhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreFontEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreTextEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextRenderingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TilemapModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIElementsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UmbraModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsCommonModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConnectModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityCurlModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityTestProtocolModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAudioModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestTextureModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestWWWModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VFXModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VRModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VehiclesModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VideoModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VirtualTexturingModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.WindModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.XRModule.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Postprocessing.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.AdaptivePerformance.Google.Android.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Profiling.Core.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.AdaptivePerformance.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.Geometry.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.PbfReader.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/MapboxAccounts/net4x/MapboxAccountsUnity.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Plugins/M2Mqtt.Net.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/ThirdParty/Mapbox.Json/Net35/Mapbox.Json.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/ThirdParty/Mapbox.IO.Compression/net35/Mapbox.IO.Compression.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.VectorTileReader.dll", "C:/PL/Projets Unity/RegattaVisionV5/Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.ExtensionMethods.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll", "C:/PL/Projets Unity/RegattaVisionV5/Library/PackageCache/com.unity.burst@1.8.17/Unity.Burst.Unsafe.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/netstandard.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Security.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ComponentModel.Composition.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Configuration.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Core.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.DataSetExtensions.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Drawing.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.EnterpriseServices.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.FileSystem.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Numerics.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Serialization.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Security.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Internals.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Transactions.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.Linq.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.dll", "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/mscorlib.dll", "ProjectSettings/BurstAotSettings_StandaloneWindows.json", "Library/BurstCache/AotSettings_StandaloneWindows.hash", "C:/PL/Projets Unity/RegattaVisionV5/Library/PackageCache/com.unity.burst@1.8.17/.Runtime/bcl.exe"], "Outputs": [], "TargetDirectories": ["Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker", "Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinkerSymbols"], "OverwriteOutputs": false, "StatSignatures": [{"File": "Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker"}, {"File": "Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker/x86_64"}, {"File": "Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinkerSymbols"}], "GlobSignatures": [{"Path": "Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker"}, {"Path": "Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker/x86_64", "Recurse": 1}, {"Path": "Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinkerSymbols", "Recurse": 1}], "DebugActionIndex": 149}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Plugins/x86_64/lib_burst_generated.dll", "DisplayName": "Copying lib_burst_generated.dll", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker/x86_64/lib_burst_generated.dll"], "Outputs": ["Build/RegattaVision3D_Data/Plugins/x86_64/lib_burst_generated.dll"], "ToBuildDependencies": [149], "DebugActionIndex": 150}, {"Annotation": "WriteText Library/Bee/artifacts/WinPlayerBuildProgram/Data/app.info", "DisplayName": "Writing app.info", "ActionType": "WriteFile", "PayloadOffset": 36629, "PayloadLength": 23, "PayloadDebugContentSnippet": "Artyzia\nRegattaVision3D", "Inputs": [], "Outputs": ["Library/Bee/artifacts/WinPlayerBuildProgram/Data/app.info"], "DebugActionIndex": 151}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/app.info", "DisplayName": "Copying app.info", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/WinPlayerBuildProgram/Data/app.info"], "Outputs": ["Build/RegattaVision3D_Data/app.info"], "ToBuildDependencies": [151], "DebugActionIndex": 152}, {"Annotation": "CopyFiles Build/RegattaVision3D_Data/Resources/unity default resources", "DisplayName": "Copying unity default resources", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources/unity default resources"], "Outputs": ["Build/RegattaVision3D_Data/Resources/unity default resources"], "DebugActionIndex": 153}, {"Annotation": "CopyFiles Build/RegattaVision3D.exe", "DisplayName": "Copying RegattaVision3D.exe", "ActionType": "CopyFiles", "Inputs": ["Temp/StagingArea/WindowsPlayer.exe"], "Outputs": ["Build/RegattaVision3D.exe"], "DebugActionIndex": 154}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll", "DisplayName": "Copying mono-2.0-bdwgc.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll"], "Outputs": ["Build/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll"], "DebugActionIndex": 155}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll", "DisplayName": "Copying MonoPosixHelper.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll"], "Outputs": ["Build/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll"], "DebugActionIndex": 156}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser", "DisplayName": "Copying Compat.browser", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser"], "DebugActionIndex": 157}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx", "DisplayName": "Copying DefaultWsdlHelpGenerator.aspx", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx"], "DebugActionIndex": 158}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/2.0/machine.config", "DisplayName": "Copying machine.config", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/2.0/machine.config"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/2.0/machine.config"], "DebugActionIndex": 159}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/2.0/settings.map", "DisplayName": "Copying settings.map", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/2.0/settings.map"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/2.0/settings.map"], "DebugActionIndex": 160}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/2.0/web.config", "DisplayName": "Copying web.config", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/2.0/web.config"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/2.0/web.config"], "DebugActionIndex": 161}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser", "DisplayName": "Copying Compat.browser", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser"], "DebugActionIndex": 162}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx", "DisplayName": "Copying DefaultWsdlHelpGenerator.aspx", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx"], "DebugActionIndex": 163}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/4.0/machine.config", "DisplayName": "Copying machine.config", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/4.0/machine.config"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/4.0/machine.config"], "DebugActionIndex": 164}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/4.0/settings.map", "DisplayName": "Copying settings.map", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/4.0/settings.map"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/4.0/settings.map"], "DebugActionIndex": 165}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/4.0/web.config", "DisplayName": "Copying web.config", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/4.0/web.config"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/4.0/web.config"], "DebugActionIndex": 166}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser", "DisplayName": "Copying Compat.browser", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser"], "DebugActionIndex": 167}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx", "DisplayName": "Copying DefaultWsdlHelpGenerator.aspx", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx"], "DebugActionIndex": 168}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/4.5/machine.config", "DisplayName": "Copying machine.config", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/4.5/machine.config"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/4.5/machine.config"], "DebugActionIndex": 169}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/4.5/settings.map", "DisplayName": "Copying settings.map", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/4.5/settings.map"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/4.5/settings.map"], "DebugActionIndex": 170}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/4.5/web.config", "DisplayName": "Copying web.config", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/4.5/web.config"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/4.5/web.config"], "DebugActionIndex": 171}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/browscap.ini", "DisplayName": "Copying browscap.ini", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/browscap.ini"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/browscap.ini"], "DebugActionIndex": 172}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/config", "DisplayName": "Co<PERSON><PERSON> config", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/config"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/config"], "DebugActionIndex": 173}, {"Annotation": "CopyFiles Build/MonoBleedingEdge/etc/mono/mconfig/config.xml", "DisplayName": "Copying config.xml", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/MonoBleedingEdge/etc/mono/mconfig/config.xml"], "Outputs": ["Build/MonoBleedingEdge/etc/mono/mconfig/config.xml"], "DebugActionIndex": 174}, {"Annotation": "CopyFiles Build/UnityCrashHandler64.exe", "DisplayName": "Copying UnityCrashHandler64.exe", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/UnityCrashHandler64.exe"], "Outputs": ["Build/UnityCrashHandler64.exe"], "DebugActionIndex": 175}, {"Annotation": "CopyFiles Build/UnityPlayer.dll", "DisplayName": "Copying UnityPlayer.dll", "ActionType": "CopyFiles", "Inputs": ["C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/UnityPlayer.dll"], "Outputs": ["Build/UnityPlayer.dll"], "DebugActionIndex": 176}, {"Annotation": "Player", "DisplayName": null, "Inputs": [], "Outputs": [], "ToBuildDependencies": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 150, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176], "OverwriteOutputs": false, "DebugActionIndex": 177}], "FileSignatures": [{"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/WinPlayerBuildProgram.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/WinPlayerBuildProgram.exe"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.BuildTools.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Core.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.CSharpSupport.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.DotNet.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.NativeProgramSupport.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Stevedore.Program.exe"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.TinyProfiler2.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.GNU.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.LLVM.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.VisualStudio.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.Xcode.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.Tools.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.TundraBackend.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Bee.VisualStudioSolution.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/BeeBuildProgramCommon.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/BeeBuildProgramCommon.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/BeeLocalCacheTool.exe"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Newtonsoft.Json.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/NiceIO.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/PlayerBuildProgramLibrary.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/ScriptCompilationBuildProgram.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/ScriptCompilationBuildProgram.exe"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/SharpYaml.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Api.Attributes.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Mdb.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Pdb.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Rocks.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Api.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Bee.BuildLogic.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Linker.Api.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline/Unity.Options.dll"}, {"File": "Library/Bee/Playere2a68edc-inputdata.json"}], "StatSignatures": [{"File": "Assets/Mapbox/Core/Plugins/sqlite/x64/sqlite3.dll"}, {"File": "Build/GameAssembly.dll"}, {"File": "Build/RegattaVision3D.sln"}, {"File": "Build/UnityCommon.props"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono"}, {"File": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources"}, {"File": "Library/Bee/Playere2a68edc-inputdata.json"}], "GlobSignatures": [{"Path": "Assets/StreamingAssets", "Recurse": 1}, {"Path": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport"}, {"Path": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono", "Recurse": 1}, {"Path": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources", "Recurse": 1}, {"Path": "C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/BuildPipeline"}, {"Path": "Library/PlayerDataCache/Win64/Data", "Recurse": 1}, {"Path": "Temp/StagingArea/Data/Plugins"}, {"Path": "Temp/StagingArea/Data/UnitySubsystems", "Recurse": 1}], "ContentDigestExtensions": [".rsp", ".dll", ".exe", ".winmd", ".pdb", ".cpp", ".cc", ".c", ".hpp", ".h", ".json", ".xml", ".txt", ".config"], "StructuredLogFileName": "Library/Bee/tundra.log.json", "StateFileName": "Library/Bee/TundraBuildState.state", "StateFileNameTmp": "Library/Bee/TundraBuildState.state.tmp", "StateFileNameMapped": "Library/Bee/TundraBuildState.state.map", "ScanCacheFileName": "Library/Bee/tundra.scancache", "ScanCacheFileNameTmp": "Library/Bee/tundra.scancache.tmp", "DigestCacheFileName": "Library/Bee/tundra.digestcache", "DigestCacheFileNameTmp": "Library/Bee/tundra.digestcache.tmp", "CachedNodeOutputDirectoryName": "Library/Bee/CachedNodeOutput", "EmitDataForBeeWhy": 0, "DirectoriesCausingImplicitDependencies": ["Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker", "Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinkerSymbols"], "NamedNodes": {"all_tundra_nodes": 0, "Player": 177}, "DefaultNodes": [0], "SharedResources": [], "Scanners": [], "Identifier": "Library/Bee/Playere2a68edc.dag.json", "PayloadsFile": "C:/PL/Projets Unity/RegattaVisionV5/Library/Bee/Playere2a68edc.dag.payloads", "RelativePathToRoot": "../.."}