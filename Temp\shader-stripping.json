{"totalVariantsIn": 10902, "totalVariantsOut": 3067, "shaders": [{"inputVariants": 2, "outputVariants": 0, "name": "Shader Graphs/DoubleSideShader", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1029}]}]}, {"inputVariants": 1365, "outputVariants": 0, "name": "Universal Render Pipeline/Lit", "pipelines": [{"inputVariants": 1365, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1162, "outputVariants": 0, "variantName": "ForwardLit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 15.7066}, {"inputVariants": 200, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.8356000000000001}, {"inputVariants": 3, "outputVariants": 0, "variantName": "Universal2D (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0654}]}]}, {"inputVariants": 3, "outputVariants": 0, "name": "Universal Render Pipeline/Unlit", "pipelines": [{"inputVariants": 3, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 3, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0915}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Shader Graphs/DoubleSideShader", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09730000000000001}]}]}, {"inputVariants": 3, "outputVariants": 0, "name": "Universal Render Pipeline/Unlit", "pipelines": [{"inputVariants": 3, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 3, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0868}]}]}, {"inputVariants": 1365, "outputVariants": 0, "name": "Universal Render Pipeline/Lit", "pipelines": [{"inputVariants": 1365, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1162, "outputVariants": 0, "variantName": "ForwardLit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 14.843300000000001}, {"inputVariants": 200, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.8385}, {"inputVariants": 3, "outputVariants": 0, "variantName": "Universal2D (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.052700000000000004}]}]}, {"inputVariants": 220, "outputVariants": 0, "name": "Shader Graphs/Easy Water", "pipelines": [{"inputVariants": 220, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 140, "outputVariants": 0, "variantName": "Universal Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.7882}, {"inputVariants": 80, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7797000000000001}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/Sampling", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "BoxDownsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0835}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BoxDownsample (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0639}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/VoxelizeShader", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07590000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0618}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0649}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0565}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/Stop NaN", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Stop NaN (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0658}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Stop NaN (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061200000000000004}]}]}, {"inputVariants": 40, "outputVariants": 40, "name": "Hidden/Universal Render Pipeline/TemporalAA", "pipelines": [{"inputVariants": 40, "outputVariants": 40, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "TemporalAA - Accumulate - Quality Very Low (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0813}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Very Low (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0673}, {"inputVariants": 4, "outputVariants": 4, "variantName": "TemporalAA - Accumulate - Quality Low (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0826}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Low (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0684}, {"inputVariants": 4, "outputVariants": 4, "variantName": "TemporalAA - Accumulate - Quality Medium (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08120000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Medium (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.065}, {"inputVariants": 4, "outputVariants": 4, "variantName": "TemporalAA - Accumulate - Quality High (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0804}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality High (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.063}, {"inputVariants": 6, "outputVariants": 6, "variantName": "TemporalAA - Accumulate - Quality Very High (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09680000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "TemporalAA - Accumulate - Quality Very High (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08420000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "TemporalAA - Copy History (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0823}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Copy History (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0704}]}]}, {"inputVariants": 6, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/CameraMotionBlur", "pipelines": [{"inputVariants": 6, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "Camera Motion Blur - Low Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0719}, {"inputVariants": 2, "outputVariants": 0, "variantName": "Camera Motion Blur - Medium Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0669}, {"inputVariants": 2, "outputVariants": 0, "variantName": "Camera Motion Blur - High Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0694}]}]}, {"inputVariants": 30, "outputVariants": 30, "name": "Hidden/Universal Render Pipeline/BokehDepthOfField", "pipelines": [{"inputVariants": 30, "outputVariants": 30, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0762}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061900000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0723}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0585}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0661}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.056900000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0614}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.058800000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0664}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0582}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.066}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0572}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.06470000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.054400000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0649}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.07250000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0748}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.060200000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0645}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.056600000000000004}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/VFX/Empty", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0678}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0558}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Core/FallbackError", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0659}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0558}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/XR/XROcclusionMesh", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0874}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0594}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/Edge Adaptive Spatial Upsampling", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "EASU (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0695}, {"inputVariants": 1, "outputVariants": 1, "variantName": "EASU (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1169}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Universal/HDRDebugView", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08420000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.11800000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1042}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0741}]}]}, {"inputVariants": 21, "outputVariants": 21, "name": "Hidden/Universal Render Pipeline/GaussianDepthOfField", "pipelines": [{"inputVariants": 21, "outputVariants": 21, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08020000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06670000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Gaussian Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.084}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0675}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.066}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.056}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Blur Vertical (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field Blur Vertical (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.057100000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Gaussian Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08020000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0645}]}]}, {"inputVariants": 36, "outputVariants": 36, "name": "Hidden/Universal Render Pipeline/Bloom", "pipelines": [{"inputVariants": 36, "outputVariants": 36, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Bloom Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1148}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bloom Prefilter (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0833}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bloom Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08170000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bloom Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0753}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bloom Blur Vertical (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0859}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bloom Blur Vertical (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0685}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Bloom Upsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1135}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bloom Upsample (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08360000000000001}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/MaterialError", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.067}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.081}]}]}, {"inputVariants": 27, "outputVariants": 27, "name": "Hidden/Universal Render Pipeline/SubpixelMorphologicalAntialiasing", "pipelines": [{"inputVariants": 27, "outputVariants": 27, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0989}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0809}, {"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10200000000000001}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0964}, {"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10070000000000001}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07540000000000001}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/LutBuilderLdr", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "LutBuilderLdr (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0664}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LutBuilderLdr (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054900000000000004}]}]}, {"inputVariants": 163, "outputVariants": 163, "name": "Hidden/Universal Render Pipeline/LensFlareDataDriven", "pipelines": [{"inputVariants": 163, "outputVariants": 163, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 28, "outputVariants": 28, "variantName": "LensFlareAdditive (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2702}, {"inputVariants": 12, "outputVariants": 12, "variantName": "LensFlareAdditive (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.15280000000000002}, {"inputVariants": 28, "outputVariants": 28, "variantName": "LensFlareScreen (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.305}, {"inputVariants": 12, "outputVariants": 12, "variantName": "LensFlareScreen (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1552}, {"inputVariants": 28, "outputVariants": 28, "variantName": "LensFlarePremultiply (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.29600000000000004}, {"inputVariants": 12, "outputVariants": 12, "variantName": "LensFlarePremultiply (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1501}, {"inputVariants": 28, "outputVariants": 28, "variantName": "LensFlareLerp (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.32180000000000003}, {"inputVariants": 12, "outputVariants": 12, "variantName": "LensFlareLerp (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1486}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0742}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0591}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/ObjectMotionVectors", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Object Motion Vectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0829}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Object Motion Vectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.056400000000000006}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/FallbackLoading", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07010000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0553}]}]}, {"inputVariants": 170, "outputVariants": 170, "name": "Hidden/Universal/CoreBlit", "pipelines": [{"inputVariants": 170, "outputVariants": 170, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.082}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.064}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.22410000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0649}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08360000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0654}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0809}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06280000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 4 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.083}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 4 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06720000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 5 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08420000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 5 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0654}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 6 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10490000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 6 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0645}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 7 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.081}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 7 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0639}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 8 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0835}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 8 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0702}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 9 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08070000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 9 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06470000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 10 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0852}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 10 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0702}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 11 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0829}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 11 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06570000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 12 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1043}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 12 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0718}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 13 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0872}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 13 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0709}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 14 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08120000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 14 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0674}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 15 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0804}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 15 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0684}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 16 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0854}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 16 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0689}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 17 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0888}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 17 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0721}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 18 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1268}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 18 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0713}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 19 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1033}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 19 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0772}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 20 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0927}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 20 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0703}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 21 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11760000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 21 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08460000000000001}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 22 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11270000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 22 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0857}, {"inputVariants": 6, "outputVariants": 6, "variantName": "BilinearDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1107}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearDebugDraw (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08420000000000001}, {"inputVariants": 6, "outputVariants": 6, "variantName": "NearestDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10010000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestDebugDraw (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0853}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/PaniniProjection", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 0, "variantName": "Panini Projection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0816}]}]}, {"inputVariants": 8, "outputVariants": 0, "name": "Hidden/Universal/BlitHDROverlay", "pipelines": [{"inputVariants": 8, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 0, "variantName": "BilinearDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0507}, {"inputVariants": 4, "outputVariants": 0, "variantName": "NearestDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0497}]}]}, {"inputVariants": 5, "outputVariants": 5, "name": "Hidden/Universal Render Pipeline/Blit", "pipelines": [{"inputVariants": 5, "outputVariants": 5, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Blit (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0767}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Blit (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06860000000000001}]}]}, {"inputVariants": 258, "outputVariants": 194, "name": "Hidden/Universal Render Pipeline/FinalPost", "pipelines": [{"inputVariants": 258, "outputVariants": 194, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 65, "outputVariants": 49, "variantName": "FinalPost (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5349}, {"inputVariants": 64, "outputVariants": 48, "variantName": "FinalPost (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5808}, {"inputVariants": 65, "outputVariants": 49, "variantName": "FinalPost (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.5574}, {"inputVariants": 64, "outputVariants": 48, "variantName": "FinalPost (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.5409}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/CameraMotionVectors", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Camera Motion Vectors (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0978}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Vectors (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0685}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/Debug/DebugReplacement", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Vertex Attributes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0847}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Vertex Attributes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0664}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/Universal/CoreBlitColorAndDepth", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0935}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0724}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0884}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0728}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Universal Render Pipeline/XR/XRMirrorView", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1183}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1272}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07780000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0629}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Hidden/Universal Render Pipeline/CopyDepth", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.18150000000000002}, {"inputVariants": 8, "outputVariants": 8, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1202}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/FallbackError", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0718}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0614}]}]}, {"inputVariants": 1921, "outputVariants": 181, "name": "Hidden/Universal Render Pipeline/UberPost", "pipelines": [{"inputVariants": 1921, "outputVariants": 181, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 961, "outputVariants": 91, "variantName": "UberPost (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 8.9756}, {"inputVariants": 960, "outputVariants": 90, "variantName": "UberPost (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 9.0065}]}]}, {"inputVariants": 7, "outputVariants": 7, "name": "Hidden/Universal Render Pipeline/Scaling Setup", "pipelines": [{"inputVariants": 7, "outputVariants": 7, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ScalingSetup (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1058}, {"inputVariants": 3, "outputVariants": 3, "variantName": "ScalingSetup (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0821}]}]}, {"inputVariants": 134, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/StencilDeferred", "pipelines": [{"inputVariants": 134, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 0, "variantName": "Stencil Volume (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0516}, {"inputVariants": 14, "outputVariants": 0, "variantName": "Deferred Punctual Light (Lit) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0653}, {"inputVariants": 14, "outputVariants": 0, "variantName": "Deferred Punctual Light (SimpleLit) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0651}, {"inputVariants": 49, "outputVariants": 0, "variantName": "Deferred Directional Light (Lit) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1018}, {"inputVariants": 49, "outputVariants": 0, "variantName": "Deferred Directional Light (SimpleLit) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0975}, {"inputVariants": 2, "outputVariants": 0, "variantName": "ClearStencilPartial (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.045700000000000005}, {"inputVariants": 2, "outputVariants": 0, "variantName": "SSAOOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0432}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "Hidden/Universal Render Pipeline/LutBuilderHdr", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "LutBuilderHdr (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1057}, {"inputVariants": 3, "outputVariants": 3, "variantName": "LutBuilderHdr (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.075}]}]}, {"inputVariants": 16, "outputVariants": 14, "name": "Shader Graphs/DoubleSideShader", "pipelines": [{"inputVariants": 16, "outputVariants": 14, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Universal Forward (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0854}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Universal Forward (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06770000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0849}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06770000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthNormalsOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0821}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthNormalsOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.076}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10940000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0737}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10980000000000001}]}]}, {"inputVariants": 133, "outputVariants": 133, "name": "Standard", "pipelines": [{"inputVariants": 133, "outputVariants": 133, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2146}, {"inputVariants": 4, "outputVariants": 4, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0999}, {"inputVariants": 26, "outputVariants": 26, "variantName": "FORWARD_DELTA (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.29550000000000004}, {"inputVariants": 13, "outputVariants": 13, "variantName": "FORWARD_DELTA (ForwardAdd) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2736}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11760000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07930000000000001}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.163}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0961}, {"inputVariants": 16, "outputVariants": 16, "variantName": "FORWARD (ForwardBase) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.202}, {"inputVariants": 4, "outputVariants": 4, "variantName": "FORWARD (ForwardBase) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.10060000000000001}, {"inputVariants": 20, "outputVariants": 20, "variantName": "FORWARD_DELTA (ForwardAdd) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.22870000000000001}, {"inputVariants": 10, "outputVariants": 10, "variantName": "FORWARD_DELTA (ForwardAdd) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.1476}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0994}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0738}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Unlit/Color", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07300000000000001}]}]}, {"inputVariants": 71, "outputVariants": 71, "name": "Mapbox/MapboxStylesPerRenderer", "pipelines": [{"inputVariants": 71, "outputVariants": 71, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.201}, {"inputVariants": 4, "outputVariants": 4, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0951}, {"inputVariants": 26, "outputVariants": 26, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2846}, {"inputVariants": 13, "outputVariants": 13, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1741}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1255}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1491}]}]}, {"inputVariants": 118, "outputVariants": 37, "name": "Universal Render Pipeline/Autodesk Interactive/AutodeskInteractive", "pipelines": [{"inputVariants": 118, "outputVariants": 37, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 48, "outputVariants": 13, "variantName": "Universal Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6263000000000001}, {"inputVariants": 36, "outputVariants": 10, "variantName": "Universal Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.4824}, {"inputVariants": 20, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.23240000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09390000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0708}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0746}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0579}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthNormals (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0847}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthNormals (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0626}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Universal 2D (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06720000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Universal 2D (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0563}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "TextMeshPro/Distance Field SSD", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1578}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0879}]}]}, {"inputVariants": 22, "outputVariants": 19, "name": "Universal Render Pipeline/Unlit", "pipelines": [{"inputVariants": 22, "outputVariants": 19, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 5, "outputVariants": 5, "variantName": "Unlit (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0979}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Unlit (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0884}, {"inputVariants": 3, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0782}, {"inputVariants": 3, "outputVariants": 3, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0743}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0673}, {"inputVariants": 3, "outputVariants": 3, "variantName": "DepthNormalsOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0745}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthNormalsOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0661}]}]}, {"inputVariants": 14, "outputVariants": 14, "name": "TextMeshPro/Mobile/Distance Field (Surface)", "pipelines": [{"inputVariants": 14, "outputVariants": 14, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1029}, {"inputVariants": 2, "outputVariants": 2, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0648}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Caster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0833}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Caster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0823}]}]}, {"inputVariants": 3431, "outputVariants": 922, "name": "Universal Render Pipeline/Lit", "pipelines": [{"inputVariants": 3431, "outputVariants": 922, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1608, "outputVariants": 446, "variantName": "ForwardLit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 20.948800000000002}, {"inputVariants": 1584, "outputVariants": 440, "variantName": "ForwardLit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 19.944100000000002}, {"inputVariants": 12, "outputVariants": 12, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1825}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1014}, {"inputVariants": 200, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.8116}, {"inputVariants": 6, "outputVariants": 6, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0999}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.085}, {"inputVariants": 6, "outputVariants": 6, "variantName": "DepthNormals (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10940000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthNormals (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08660000000000001}, {"inputVariants": 3, "outputVariants": 0, "variantName": "Universal2D (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.048100000000000004}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "TextMeshPro/Mobile/Distance Field Overlay", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1409}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1389}]}]}, {"inputVariants": 71, "outputVariants": 71, "name": "Mapbox/MapboxStyles", "pipelines": [{"inputVariants": 71, "outputVariants": 71, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1976}, {"inputVariants": 4, "outputVariants": 4, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.10830000000000001}, {"inputVariants": 26, "outputVariants": 26, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.275}, {"inputVariants": 13, "outputVariants": 13, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1703}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1182}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08560000000000001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "TextMeshPro/Mobile/Distance Field - Masking", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11850000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0813}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "TextMeshPro/Sprite", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1213}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1048}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Shader Graph/FallbackError", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0665}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.058800000000000005}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "Skybox/Procedural", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1213}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1101}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "TextMeshPro/Distance Field", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 32, "outputVariants": 32, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3235}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1927}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "TextMeshPro/Mobile/Distance Field", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 32, "outputVariants": 32, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3875}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.18630000000000002}]}]}, {"inputVariants": 466, "outputVariants": 142, "name": "Shader Graphs/Easy Water", "pipelines": [{"inputVariants": 466, "outputVariants": 142, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 192, "outputVariants": 52, "variantName": "Universal Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 2.4113}, {"inputVariants": 144, "outputVariants": 40, "variantName": "Universal Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.7571}, {"inputVariants": 80, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7642}, {"inputVariants": 16, "outputVariants": 16, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2038}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0891}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11910000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09480000000000001}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DepthNormals (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1273}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthNormals (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1024}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Universal 2D (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10640000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Universal 2D (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0747}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Skybox/Cubemap", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0916}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0567}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Legacy Shaders/Particles/Alpha Blended Premultiply", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0855}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0656}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Masked/DepthMask", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0656}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.057100000000000005}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "Legacy Shaders/VertexLit", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11910000000000001}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.11910000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0684}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0568}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0886}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0649}]}]}, {"inputVariants": 47, "outputVariants": 47, "name": "Legacy Shaders/Diffuse", "pipelines": [{"inputVariants": 47, "outputVariants": 47, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2021}, {"inputVariants": 4, "outputVariants": 4, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1029}, {"inputVariants": 10, "outputVariants": 10, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.13920000000000002}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1159}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11810000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1015}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Internal-StencilWrite", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0665}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0553}]}]}, {"inputVariants": 30, "outputVariants": 30, "name": "Hidden/Internal-DepthNormalsTexture", "pipelines": [{"inputVariants": 30, "outputVariants": 30, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0666}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0536}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0651}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0533}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0606}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.0557}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.18280000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.057800000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0648}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.0545}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.063}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0555}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0648}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0531}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0659}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.0541}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0634}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.0545}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0635}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.0543}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "Hidden/Internal-ScreenSpaceShadows", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.12840000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0918}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.1197}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.10010000000000001}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.11520000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.0819}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.1143}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.10500000000000001}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Internal-CombineDepthNormals", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0683}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.057800000000000004}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/BlitCopy", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0673}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.055400000000000005}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/BlitCopyDepth", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.066}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0548}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/ConvertTexture", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0653}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0548}]}]}, {"inputVariants": 81, "outputVariants": 81, "name": "Hidden/Internal-DeferredShading", "pipelines": [{"inputVariants": 81, "outputVariants": 81, "pipeline": "", "variants": [{"inputVariants": 52, "outputVariants": 52, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.47840000000000005}, {"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.28240000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0689}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.059500000000000004}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "Hidden/Internal-DeferredReflections", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0813}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.081}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06810000000000001}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "Hidden/Internal-MotionVectors", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0731}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0597}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0653}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0579}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0816}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06760000000000001}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Internal-Flare", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0695}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0609}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Internal-Halo", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0685}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0594}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/BlitCopyWithDepth", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0674}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0587}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/BlitToDepth", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0692}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0597}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/BlitToDepth_MSAA", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0687}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0636}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/BlitCopyHDRTonemap", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0719}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061700000000000005}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "Hidden/Internal-DebugPattern", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0732}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0614}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0678}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0597}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0874}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07250000000000001}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-GUITextureClip", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0704}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0587}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0703}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0589}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-GUITextureClipText", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.069}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0601}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.07010000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0716}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-GUITexture", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0826}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0734}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.1155}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.09480000000000001}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-GUITextureBlit", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0878}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0771}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.111}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0814}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-GUIRoundedRect", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0689}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061500000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0717}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0568}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-GUIRoundedRectWithColorPerBorder", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0707}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.059500000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0689}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.059000000000000004}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-UIRDefault", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07010000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0606}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.07780000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.058300000000000005}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Internal-UIRAtlasBlitCopy", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0682}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0594}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-UIRDefaultWorld", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0714}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.057100000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0685}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.055600000000000004}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Sprites/Default", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11910000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0936}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Sprites/Mask", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.12150000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0893}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "UI/Default", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1255}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08650000000000001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "UI/DefaultETC1", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1255}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08850000000000001}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/CubeBlur", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0707}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.057100000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0682}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0558}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/CubeCopy", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0661}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0574}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0663}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.055200000000000006}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/CubeBlend", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0669}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.058800000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.06570000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.054200000000000005}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/VR/BlitTexArraySlice", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0641}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.056600000000000004}]}]}, {"inputVariants": 30, "outputVariants": 30, "name": "Hidden/Internal-ODSWorldTexture", "pipelines": [{"inputVariants": 30, "outputVariants": 30, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0658}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.058100000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0632}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0534}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0623}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.0567}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0639}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.055}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.064}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.0567}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0627}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0555}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0634}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0568}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0653}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.0579}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.06380000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.054200000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.06470000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.055}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Internal-CubemapToEquirect", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0646}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.055400000000000005}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/VR/BlitFromTex2DToTexArraySlice", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0597}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.064}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0634}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0541}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Geometry)", "stripTimeMs": 0.0545}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/VideoComposite", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0665}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.055600000000000004}]}]}, {"inputVariants": 45, "outputVariants": 45, "name": "Hidden/VideoDecode", "pipelines": [{"inputVariants": 45, "outputVariants": 45, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0809}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0646}, {"inputVariants": 4, "outputVariants": 4, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08070000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06330000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08030000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0634}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09290000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06470000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0847}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0809}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0639}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0786}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0661}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06280000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0546}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Compositing", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0656}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.057300000000000004}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/TextCore/Distance Field SSD", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0661}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.056900000000000006}]}]}]}