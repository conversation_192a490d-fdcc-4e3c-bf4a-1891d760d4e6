-target:library
-out:"Library/Bee/artifacts/1900b0aP.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/1900b0aP.dag/Assembly-CSharp.ref.dll"
-define:UNITY_2022_3_47
-define:UNITY_2022_3
-define:UNITY_2022
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION
-define:ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT
-define:ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE
-define:PLATFORM_STANDALONE
-define:TEXTCORE_1_0_OR_NEWER
-define:PLATFORM_STANDALONE_WIN
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:UNITY_POST_PROCESSING_STACK_V2
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/Mapbox/Core/Plugins/Mapbox/MapboxAccounts/net4x/MapboxAccountsUnity.dll"
-r:"Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.ExtensionMethods.dll"
-r:"Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.Geometry.dll"
-r:"Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.PbfReader.dll"
-r:"Assets/Mapbox/Core/Plugins/Mapbox/vector-tile-cs/net46/Mapbox.VectorTile.VectorTileReader.dll"
-r:"Assets/Mapbox/Core/Plugins/ThirdParty/Mapbox.IO.Compression/net35/Mapbox.IO.Compression.dll"
-r:"Assets/Mapbox/Core/Plugins/ThirdParty/Mapbox.Json/Net35/Mapbox.Json.dll"
-r:"Assets/Plugins/M2Mqtt.Net.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ProfilerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.AdaptivePerformance.Google.Android.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.AdaptivePerformance.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.InputSystem.ForUI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.Postprocessing.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.Profiling.Core.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Universal.Shaders.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.VisualEffectGraph.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Core.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Flow.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.State.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
"Assets/Bitgem/StylisedWater/URP/Scripts/Bitgem/Core/FlagEnumAttribute.cs"
"Assets/Bitgem/StylisedWater/URP/Scripts/Bitgem/VFX/StylisedWater/WaterVolumeBase.cs"
"Assets/Bitgem/StylisedWater/URP/Scripts/Bitgem/VFX/StylisedWater/WaterVolumeBox.cs"
"Assets/Bitgem/StylisedWater/URP/Scripts/Bitgem/VFX/StylisedWater/WaterVolumeHelper.cs"
"Assets/Bitgem/StylisedWater/URP/Scripts/Bitgem/VFX/StylisedWater/WaterVolumeTransforms.cs"
"Assets/Bitgem/StylisedWater/URP/Scripts/Bitgem/VFX/StylisedWater/WateverVolumeFloater.cs"
"Assets/Mapbox/Core/cheap-ruler-cs/CheapRuler.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Directions/DirectionResource.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Directions/Directions.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Directions/Overview.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Directions/Response/Annotation.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Directions/Response/DirectionsResponse.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Directions/Response/Intersection.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Directions/Response/Leg.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Directions/Response/Maneuver.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Directions/Response/Route.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Directions/Response/Step.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Directions/Response/Waypoint.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Directions/RoutingProfile.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Geocoding/ForwardGeocodeResource.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Geocoding/Geocoder.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Geocoding/GeocodeResource.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Geocoding/Response/Feature.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Geocoding/Response/GeocodeResponse.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Geocoding/Response/Geometry.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Geocoding/ReverseGeocodeResource.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/CanonicalTileId.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/ClassicRasterTile.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/ClassicRetinaRasterTile.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/Map.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/MapUtils.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/RasterTile.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/RawPngRasterTile.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/RetinaRasterTile.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/Tile.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/TileCover.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/TileErrorEventArgs.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/TileResource.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/UnwrappedTileId.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Map/VectorTile.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/MapMatching/MapMatcher.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/MapMatching/MapMatchingParameters.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/MapMatching/MapMatchingResource.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/MapMatching/MapMatchingResponse.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/MapMatching/MatchObject.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/MapMatching/Tracepoint.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/Cache/CacheItem.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/Cache/CachingWebFileSource.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/Cache/ICache.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/Cache/MemoryCache.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/Cache/SQLiteCache/SQLiteCache.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/Cache/SQLiteCache/Tiles.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/Cache/SQLiteCache/Tilesets.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/FileSource.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/HTTPRequestNonThreaded.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/HTTPRequestThreaded.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/IAsyncRequest.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/IAsyncRequestFactory.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/IFileSource.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/IResource.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/Resource.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/Response.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/SQLite/SQLite.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/TileJSON/TileJSON.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/TileJSON/TileJSONObjectVectorLayer.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Platform/TileJSON/TileJSONReponse.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Tokens/MapboxToken.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Tokens/MapboxTokenApi.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/BearingFilter.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/CircularBuffer.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/Compression.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/Constants.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/GeoCoordinateBounds.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/IObservable.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/IObserver.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/JsonConverters/BboxToGeoCoordinateBoundsConverter.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/JsonConverters/JsonConverters.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/JsonConverters/LonLatToGeoCoordinateConverter.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/JsonConverters/PolylineToGeoCoordinateListConverter.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/PolylineUtils.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/UnixTimestampUtils.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/Vector2d/Mathd.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/Vector2d/RectD.cs"
"Assets/Mapbox/Core/mapbox-sdk-cs/Utils/Vector2d/Vector2d.cs"
"Assets/Mapbox/Core/Plugins/Android/UniAndroidPermission/UniAndroidPermission.cs"
"Assets/Mapbox/Core/probe-extractor-cs/Probe.cs"
"Assets/Mapbox/Core/probe-extractor-cs/ProbeExtractor.cs"
"Assets/Mapbox/Examples/1_DataExplorer/HighlightBuildings.cs"
"Assets/Mapbox/Examples/1_DataExplorer/Scripts/TextMeshOutline.cs"
"Assets/Mapbox/Examples/2_AstronautGame/AstronautGame/Astronaut/AstronautDirections.cs"
"Assets/Mapbox/Examples/2_AstronautGame/AstronautGame/Astronaut/AstronautMouseController.cs"
"Assets/Mapbox/Examples/2_AstronautGame/AstronautGame/Astronaut/CharacterMovement.cs"
"Assets/Mapbox/Examples/3_POIPlacement/POIPlacementScriptExample.cs"
"Assets/Mapbox/Examples/4_ReplaceFeatures/Scripts/BlinkLight.cs"
"Assets/Mapbox/Examples/5_Playground/Scripts/DirectionsExample.cs"
"Assets/Mapbox/Examples/5_Playground/Scripts/ForwardGeocoderExample.cs"
"Assets/Mapbox/Examples/5_Playground/Scripts/LogLocationProviderData.cs"
"Assets/Mapbox/Examples/5_Playground/Scripts/RasterTileExample.cs"
"Assets/Mapbox/Examples/5_Playground/Scripts/ReverseGeocoderExample.cs"
"Assets/Mapbox/Examples/5_Playground/Scripts/VectorTileExample.cs"
"Assets/Mapbox/Examples/6_ZoomableMap/Scripts/SetCameraHeight.cs"
"Assets/Mapbox/Examples/6_ZoomableMap/Scripts/SpawnOnMap.cs"
"Assets/Mapbox/Examples/7_Globe/Scripts/DragRotate.cs"
"Assets/Mapbox/Examples/7_Globe/Scripts/SpawnOnGlobeExample.cs"
"Assets/Mapbox/Examples/8_VoxelMap/Scripts/TextureScaler.cs"
"Assets/Mapbox/Examples/8_VoxelMap/Scripts/VoxelData.cs"
"Assets/Mapbox/Examples/8_VoxelMap/Scripts/VoxelFetcher.cs"
"Assets/Mapbox/Examples/8_VoxelMap/Scripts/VoxelTile.cs"
"Assets/Mapbox/Examples/Scripts/CameraBillboard.cs"
"Assets/Mapbox/Examples/Scripts/CameraMovement.cs"
"Assets/Mapbox/Examples/Scripts/ChangeShadowDistance.cs"
"Assets/Mapbox/Examples/Scripts/DragableDirectionWaypoint.cs"
"Assets/Mapbox/Examples/Scripts/FeatureSelectionDetector.cs"
"Assets/Mapbox/Examples/Scripts/FeatureUiMarker.cs"
"Assets/Mapbox/Examples/Scripts/ForwardGeocodeUserInput.cs"
"Assets/Mapbox/Examples/Scripts/HeroBuildingSelectionUserInput.cs"
"Assets/Mapbox/Examples/Scripts/HighlightFeature.cs"
"Assets/Mapbox/Examples/Scripts/ImmediatePositionWithLocationProvider.cs"
"Assets/Mapbox/Examples/Scripts/LabelTextSetter.cs"
"Assets/Mapbox/Examples/Scripts/LoadingPanelController.cs"
"Assets/Mapbox/Examples/Scripts/LocationStatus.cs"
"Assets/Mapbox/Examples/Scripts/MakiHelper.cs"
"Assets/Mapbox/Examples/Scripts/ObjectInspectorModifier.cs"
"Assets/Mapbox/Examples/Scripts/PoiLabelTextSetter.cs"
"Assets/Mapbox/Examples/Scripts/PoiMarkerHelper.cs"
"Assets/Mapbox/Examples/Scripts/PositionWithLocationProvider.cs"
"Assets/Mapbox/Examples/Scripts/QuadTreeCameraMovement.cs"
"Assets/Mapbox/Examples/Scripts/ReloadMap.cs"
"Assets/Mapbox/Examples/Scripts/ReverseGeocodeUserInput.cs"
"Assets/Mapbox/Examples/Scripts/RotateWithLocationProvider.cs"
"Assets/Mapbox/Examples/Scripts/TrafficUvAnimator.cs"
"Assets/Mapbox/Examples/Scripts/UpdateMapWithLocationProvider.cs"
"Assets/Mapbox/Unity/Constants.cs"
"Assets/Mapbox/Unity/DataContainers/CameraBoundsTileProviderOptions.cs"
"Assets/Mapbox/Unity/DataContainers/ColliderOptions.cs"
"Assets/Mapbox/Unity/DataContainers/ElevationModificationOptions.cs"
"Assets/Mapbox/Unity/DataContainers/ElevationRequiredOptions.cs"
"Assets/Mapbox/Unity/DataContainers/ExtentOptions.cs"
"Assets/Mapbox/Unity/DataContainers/GeometryExtrusionOptions.cs"
"Assets/Mapbox/Unity/DataContainers/GeometryMaterialOptions.cs"
"Assets/Mapbox/Unity/DataContainers/ImageryRasterOptions.cs"
"Assets/Mapbox/Unity/DataContainers/LayerModifierOptions.cs"
"Assets/Mapbox/Unity/DataContainers/LayerPerformanceOptions.cs"
"Assets/Mapbox/Unity/DataContainers/LayerSourceOptions.cs"
"Assets/Mapbox/Unity/DataContainers/LineGeometryOptions.cs"
"Assets/Mapbox/Unity/DataContainers/LocationPrefabCategoryOptions.cs"
"Assets/Mapbox/Unity/DataContainers/MapboxDataProperty.cs"
"Assets/Mapbox/Unity/DataContainers/MapboxEnums.cs"
"Assets/Mapbox/Unity/DataContainers/MapExtentOptions.cs"
"Assets/Mapbox/Unity/DataContainers/MapLocationOptions.cs"
"Assets/Mapbox/Unity/DataContainers/MapOptions.cs"
"Assets/Mapbox/Unity/DataContainers/MapPlacementOptions.cs"
"Assets/Mapbox/Unity/DataContainers/MapScalingOptions.cs"
"Assets/Mapbox/Unity/DataContainers/RangeAroundTransformTileProviderOptions.cs"
"Assets/Mapbox/Unity/DataContainers/RangeTileProviderOptions.cs"
"Assets/Mapbox/Unity/DataContainers/SpawnPrefabOptions.cs"
"Assets/Mapbox/Unity/DataContainers/TerrainColliderOptions.cs"
"Assets/Mapbox/Unity/DataContainers/TerrainSideWallOptions.cs"
"Assets/Mapbox/Unity/DataContainers/TileJsonData.cs"
"Assets/Mapbox/Unity/DataContainers/TileStats.cs"
"Assets/Mapbox/Unity/DataContainers/TileStatsFetcher.cs"
"Assets/Mapbox/Unity/DataContainers/UnifiedMapOptions.cs"
"Assets/Mapbox/Unity/DataContainers/UnityLayerOptions.cs"
"Assets/Mapbox/Unity/LayerProperties/CoreVectorLayerProperties.cs"
"Assets/Mapbox/Unity/LayerProperties/ElevationLayerProperties.cs"
"Assets/Mapbox/Unity/LayerProperties/ImageryLayerProperties.cs"
"Assets/Mapbox/Unity/LayerProperties/LayerProperties.cs"
"Assets/Mapbox/Unity/LayerProperties/PrefabItemOptions.cs"
"Assets/Mapbox/Unity/LayerProperties/PresetSubLayerPropertiesFetcher.cs"
"Assets/Mapbox/Unity/LayerProperties/VectorFilterOptions.cs"
"Assets/Mapbox/Unity/LayerProperties/VectorLayerProperties.cs"
"Assets/Mapbox/Unity/LayerProperties/VectorSubLayerProperties.cs"
"Assets/Mapbox/Unity/Location/AbstractEditorLocationProvider.cs"
"Assets/Mapbox/Unity/Location/AbstractLocationProvider.cs"
"Assets/Mapbox/Unity/Location/AngleSmoothing/AngleSmoothingAbstractBase.cs"
"Assets/Mapbox/Unity/Location/AngleSmoothing/AngleSmoothingAverage.cs"
"Assets/Mapbox/Unity/Location/AngleSmoothing/AngleSmoothingEMA.cs"
"Assets/Mapbox/Unity/Location/AngleSmoothing/AngleSmoothingLowPass.cs"
"Assets/Mapbox/Unity/Location/AngleSmoothing/AngleSmoothingNoOp.cs"
"Assets/Mapbox/Unity/Location/AngleSmoothing/IAngleSmoothing.cs"
"Assets/Mapbox/Unity/Location/DeviceLocationProvider.cs"
"Assets/Mapbox/Unity/Location/DeviceLocationProviderAndroidNative.cs"
"Assets/Mapbox/Unity/Location/EditorLocationProvider.cs"
"Assets/Mapbox/Unity/Location/EditorLocationProviderLocationLog.cs"
"Assets/Mapbox/Unity/Location/ILocationProvider.cs"
"Assets/Mapbox/Unity/Location/Location.cs"
"Assets/Mapbox/Unity/Location/LocationArrayEditorLocationProvider.cs"
"Assets/Mapbox/Unity/Location/LocationProviderFactory.cs"
"Assets/Mapbox/Unity/Location/LocationSmoothing/KalmanFilter.cs"
"Assets/Mapbox/Unity/Location/Logging/LocationLogAbstractBase.cs"
"Assets/Mapbox/Unity/Location/Logging/LocationLogReader.cs"
"Assets/Mapbox/Unity/Location/Logging/LocationLogWriter.cs"
"Assets/Mapbox/Unity/Location/TransformLocationProvider.cs"
"Assets/Mapbox/Unity/Location/UnityLocationWrappers/IMapboxLocationInfo.cs"
"Assets/Mapbox/Unity/Location/UnityLocationWrappers/IMapboxLocationService.cs"
"Assets/Mapbox/Unity/Location/UnityLocationWrappers/MapboxLocationInfoMock.cs"
"Assets/Mapbox/Unity/Location/UnityLocationWrappers/MapboxLocationInfoUnityWrapper.cs"
"Assets/Mapbox/Unity/Location/UnityLocationWrappers/MapboxLocationServiceMock.cs"
"Assets/Mapbox/Unity/Location/UnityLocationWrappers/MapboxLocationServiceUnityWrapper.cs"
"Assets/Mapbox/Unity/Map/AbstractMap.cs"
"Assets/Mapbox/Unity/Map/AbstractMapVisualizer.cs"
"Assets/Mapbox/Unity/Map/InitializeMapWithLocationProvider.cs"
"Assets/Mapbox/Unity/Map/Interfaces/IMap.cs"
"Assets/Mapbox/Unity/Map/Interfaces/IMapPlacementStrategy.cs"
"Assets/Mapbox/Unity/Map/Interfaces/IMapScalingStrategy.cs"
"Assets/Mapbox/Unity/Map/Interfaces/ITileProvider.cs"
"Assets/Mapbox/Unity/Map/Interfaces/IUnifiedMap.cs"
"Assets/Mapbox/Unity/Map/MapVisualizer.cs"
"Assets/Mapbox/Unity/Map/Strategies/MapPlacementAtLocationCenterStrategy.cs"
"Assets/Mapbox/Unity/Map/Strategies/MapPlacementAtTileCenterStrategy.cs"
"Assets/Mapbox/Unity/Map/Strategies/MapScalingAtUnityScaleStrategy.cs"
"Assets/Mapbox/Unity/Map/Strategies/MapScalingAtWorldScaleStrategy.cs"
"Assets/Mapbox/Unity/Map/TileProviders/AbstractTileProvider.cs"
"Assets/Mapbox/Unity/Map/TileProviders/GlobeTileProvider.cs"
"Assets/Mapbox/Unity/Map/TileProviders/QuadTreeMapVisualizer.cs"
"Assets/Mapbox/Unity/Map/TileProviders/QuadTreeTileProvider.cs"
"Assets/Mapbox/Unity/Map/TileProviders/RangeAroundTransformTileProvider.cs"
"Assets/Mapbox/Unity/Map/TileProviders/RangeTileProvider.cs"
"Assets/Mapbox/Unity/Map/TileProviders/TileErrorHandler.cs"
"Assets/Mapbox/Unity/MapboxAccess.cs"
"Assets/Mapbox/Unity/MeshGeneration/Components/FeatureBehaviour.cs"
"Assets/Mapbox/Unity/MeshGeneration/Components/TextureSelector.cs"
"Assets/Mapbox/Unity/MeshGeneration/Components/VertexDebugger.cs"
"Assets/Mapbox/Unity/MeshGeneration/Components/VertexDebuggerGizmo.cs"
"Assets/Mapbox/Unity/MeshGeneration/Data/AtlasInfo.cs"
"Assets/Mapbox/Unity/MeshGeneration/Data/FeatureCollectionBase.cs"
"Assets/Mapbox/Unity/MeshGeneration/Data/KdTree/DistanceFunctions.cs"
"Assets/Mapbox/Unity/MeshGeneration/Data/KdTree/IntervalHeap.cs"
"Assets/Mapbox/Unity/MeshGeneration/Data/KdTree/KDNode.cs"
"Assets/Mapbox/Unity/MeshGeneration/Data/KdTree/KDTree.cs"
"Assets/Mapbox/Unity/MeshGeneration/Data/KdTree/MinHeap.cs"
"Assets/Mapbox/Unity/MeshGeneration/Data/KdTree/NearestNeighbour.cs"
"Assets/Mapbox/Unity/MeshGeneration/Data/KdTreeCollection.cs"
"Assets/Mapbox/Unity/MeshGeneration/Data/MeshData.cs"
"Assets/Mapbox/Unity/MeshGeneration/Data/UnityTile.cs"
"Assets/Mapbox/Unity/MeshGeneration/Data/VectorFeatureUnity.cs"
"Assets/Mapbox/Unity/MeshGeneration/Enums/TilePropertyState.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/AbstractTileFactory.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/DirectionsFactory.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/ImageDataFetcher.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/MapImageFactory.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/TerrainDataFetcher.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/TerrainFactoryBase.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/TerrainStrategies/ElevatedTerrainStrategy.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/TerrainStrategies/ElevatedTerrainWithSidesStrategy.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/TerrainStrategies/ElevationBasedTerrainStrategy.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/TerrainStrategies/FlatSphereTerrainStrategy.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/TerrainStrategies/FlatTerrainStrategy.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/TerrainStrategies/LowPolyTerrainStrategy.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/TerrainStrategies/TerrainStrategy.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/TileProcessFinishedEventArgs.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/VectorDataFetcher.cs"
"Assets/Mapbox/Unity/MeshGeneration/Factories/VectorTileFactory.cs"
"Assets/Mapbox/Unity/MeshGeneration/Filters/FilterBase.cs"
"Assets/Mapbox/Unity/MeshGeneration/Filters/HeightFilter.cs"
"Assets/Mapbox/Unity/MeshGeneration/Filters/TypeFilter.cs"
"Assets/Mapbox/Unity/MeshGeneration/Interfaces/IFeaturePropertySettable.cs"
"Assets/Mapbox/Unity/MeshGeneration/Interfaces/LayerVisualizerBase.cs"
"Assets/Mapbox/Unity/MeshGeneration/LayerVisualizers/LocationPrefabsLayerVisualizer.cs"
"Assets/Mapbox/Unity/MeshGeneration/LayerVisualizers/VectorLayerVisualizer.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/AddMonoBehavioursModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/AddMonoBehavioursModifierType.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/AddToCollectionModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/ColliderModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/DisableMeshRendererModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/FeatureBehaviourModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/LayerModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/MapboxStylesColorModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/MaterialModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/NoiseOffsetModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/PrefabModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/SpawnInsideModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/TagModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/TextureModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/GameObjectModifiers/TextureMonoBehaviourModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/IReplaceable.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/IReplacementCriteria.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MergedModifierStack.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshGenerationBase.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifiers/ChamferHeightModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifiers/Earcut.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifiers/HeightModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifiers/LineMeshModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifiers/LoftModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifiers/PolygonMeshModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifiers/ReplaceFeatureCollectionModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifiers/ReplaceFeatureModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifiers/SmoothLineModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifiers/SnapTerrainModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifiers/SnapTerrainRaycastModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/MeshModifiers/UvModifier.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/ModifierBase.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/ModifierStack.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/ModifierStackBase.cs"
"Assets/Mapbox/Unity/MeshGeneration/Modifiers/TextureSideWallModifier.cs"
"Assets/Mapbox/Unity/SourceLayers/AbstractLayer.cs"
"Assets/Mapbox/Unity/SourceLayers/IImageryLayer.cs"
"Assets/Mapbox/Unity/SourceLayers/ILayer.cs"
"Assets/Mapbox/Unity/SourceLayers/ImageryLayer.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerBehaviorModifiers.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerColliderOptions.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerColorStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerCoreOptions.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerCustomStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerCustomStyleAtlas.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerCustomStyleAtlasWithColorPallete.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerCustomStyleOptions.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerCustomStyleTiled.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerDarkStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerExtrusionOptions.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerFantasyStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerLightStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerLineGeometryOptions.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerModeling.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerRealisticStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerSimpleStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/ISubLayerTexturing.cs"
"Assets/Mapbox/Unity/SourceLayers/ITerrainLayer.cs"
"Assets/Mapbox/Unity/SourceLayers/MapboxDefaultElevation.cs"
"Assets/Mapbox/Unity/SourceLayers/MapboxDefaultImagery.cs"
"Assets/Mapbox/Unity/SourceLayers/MapboxDefaultStyles.cs"
"Assets/Mapbox/Unity/SourceLayers/MapboxDefaultVector.cs"
"Assets/Mapbox/Unity/SourceLayers/SubLayerBehaviorModifiers.cs"
"Assets/Mapbox/Unity/SourceLayers/SubLayerColorStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/SubLayerCustomStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/SubLayerCustomStyleAtlas.cs"
"Assets/Mapbox/Unity/SourceLayers/SubLayerCustomStyleAtlasWithColorPallete.cs"
"Assets/Mapbox/Unity/SourceLayers/SubLayerCustomStyleTiled.cs"
"Assets/Mapbox/Unity/SourceLayers/SubLayerDarkStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/SubLayerFantasyStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/SubLayerLightStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/SubLayerModeling.cs"
"Assets/Mapbox/Unity/SourceLayers/SubLayerRealisticStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/SubLayerSimpleStyle.cs"
"Assets/Mapbox/Unity/SourceLayers/TerrainLayer.cs"
"Assets/Mapbox/Unity/SourceLayers/VectorLayer.cs"
"Assets/Mapbox/Unity/Styling/ScriptablePalette.cs"
"Assets/Mapbox/Unity/Telemetry/ITelemetryLibrary.cs"
"Assets/Mapbox/Unity/Telemetry/TelemetryAndroid.cs"
"Assets/Mapbox/Unity/Telemetry/TelemetryDummy.cs"
"Assets/Mapbox/Unity/Telemetry/TelemetryEditor.cs"
"Assets/Mapbox/Unity/Telemetry/TelemetryFactory.cs"
"Assets/Mapbox/Unity/Telemetry/TelemetryFallback.cs"
"Assets/Mapbox/Unity/Telemetry/TelemetryIos.cs"
"Assets/Mapbox/Unity/Telemetry/TelemetryWebgl.cs"
"Assets/Mapbox/Unity/Utilities/AndroidSettings.cs"
"Assets/Mapbox/Unity/Utilities/Console.cs"
"Assets/Mapbox/Unity/Utilities/Conversions.cs"
"Assets/Mapbox/Unity/Utilities/DebugTools/SceneData.cs"
"Assets/Mapbox/Unity/Utilities/DebugTools/ScenesList.cs"
"Assets/Mapbox/Unity/Utilities/DescriptionAttribute.cs"
"Assets/Mapbox/Unity/Utilities/DontDestroyOnLoad.cs"
"Assets/Mapbox/Unity/Utilities/GameObjectExtensions.cs"
"Assets/Mapbox/Unity/Utilities/GeocodeAttribute.cs"
"Assets/Mapbox/Unity/Utilities/HTTPRequest.cs"
"Assets/Mapbox/Unity/Utilities/MapVisualizerPerformance.cs"
"Assets/Mapbox/Unity/Utilities/NodeEditorElementAttribute.cs"
"Assets/Mapbox/Unity/Utilities/ObjectPool.cs"
"Assets/Mapbox/Unity/Utilities/OpenUrlOnButtonClick.cs"
"Assets/Mapbox/Unity/Utilities/PolygonUtils.cs"
"Assets/Mapbox/Unity/Utilities/Runnable.cs"
"Assets/Mapbox/Unity/Utilities/Singleton.cs"
"Assets/Mapbox/Unity/Utilities/SingletonScriptableObject.cs"
"Assets/Mapbox/Unity/Utilities/StyleSearchAttribute.cs"
"Assets/Mapbox/Unity/Utilities/TelemetryConfigurationButton.cs"
"Assets/Mapbox/Unity/Utilities/VectorEntity.cs"
"Assets/Mapbox/Unity/Utilities/VectorExtensions.cs"
"Assets/SceneChanger.cs"
"Assets/Scenes/AppControl.cs"
"Assets/Script/AlertVisualEffect.cs"
"Assets/Script/BoatAlertHandler.cs"
"Assets/Script/BoatGPSManager.cs"
"Assets/Script/BoatInitializer.cs"
"Assets/Script/BoatMovementController.cs"
"Assets/Script/BoatWaypointMover.cs"
"Assets/Script/Camera/RegattaCameraSwitcher.cs"
"Assets/Script/Camera/RegattaHelicopterCamera.cs"
"Assets/Script/Camera/RegattaTacticalCamera.cs"
"Assets/Script/Camera/RegattaTVCamera.cs"
"Assets/Script/CameraDirector.cs"
"Assets/Script/CameraRotationController.cs"
"Assets/Script/Data/RaceData.cs"
"Assets/Script/Data/TrackerData.cs"
"Assets/Script/DataReceiver.cs"
"Assets/Script/DirectGPSTeleporter.cs"
"Assets/Script/FlagManager.cs"
"Assets/Script/FloatingBoatUI.cs"
"Assets/Script/GeneralRankCalc.cs"
"Assets/Script/GPS/AbstractGPSTracker.cs"
"Assets/Script/GPS/ESP32GPSTracker.cs"
"Assets/Script/GPS/FirebaseGPSTracker.cs"
"Assets/Script/GPS/GPSConfigurationManager.cs"
"Assets/Script/GPS/GPSPositionConverter.cs"
"Assets/Script/GPS/GPSToWaypointAdapter.cs"
"Assets/Script/GPS/GPSWaypointDemo.cs"
"Assets/Script/GPS/GPSWaypointIntegrator.cs"
"Assets/Script/GPS/GPSWaypointSetup.cs"
"Assets/Script/GPS/MeshtasticGPSTracker.cs"
"Assets/Script/GPS/MeshtasticUDPReceiver.cs"
"Assets/Script/GPS/SerialGPSTracker.cs"
"Assets/Script/GPS/SerialReader.cs"
"Assets/Script/GPS/udp.cs"
"Assets/Script/GPSRigidbodyFixer.cs"
"Assets/Script/ImprovedTeleportationManager.cs"
"Assets/Script/InitializationTeleporter.cs"
"Assets/Script/ManualBuoyPlacer.cs"
"Assets/Script/ManualTeleportTrigger.cs"
"Assets/Script/MarineEffectsController.cs"
"Assets/Script/PositionYMonitor.cs"
"Assets/Script/PythonLauncher.cs"
"Assets/Script/RaceController.cs"
"Assets/Script/RaceEvents.cs"
"Assets/Script/RaceLineManager.cs"
"Assets/Script/RaceResultsSaver.cs"
"Assets/Script/RaceSession.cs"
"Assets/Script/RaceTimer.cs"
"Assets/Script/RankingRow.cs"
"Assets/Script/RecordingExportUI.cs"
"Assets/Script/RecordingManager.cs"
"Assets/Script/RegattaBoatData.cs"
"Assets/Script/RegattaCameraController.cs"
"Assets/Script/RegattaData.cs"
"Assets/Script/RegattaSimulator.cs"
"Assets/Script/ResultsRow.cs"
"Assets/Script/ResultsToggle.cs"
"Assets/Script/SecuWalkers/alert-system.cs"
"Assets/Script/SecuWalkers/data-models.cs"
"Assets/Script/SecuWalkers/gps-data-fetcher.cs"
"Assets/Script/SecuWalkers/marker-manager.cs"
"Assets/Script/SecuWalkers/tracker-settings.cs"
"Assets/Script/shaders/CircleRing.cs"
"Assets/Script/SimpleWindController.cs"
"Assets/Script/SoundManager.cs"
"Assets/Script/SpectatorCameraController.cs"
"Assets/Script/StandingEntry.cs"
"Assets/Script/StartBuoyPosition.cs"
"Assets/Script/StartingLine.cs"
"Assets/Script/StartSequenceTimer.cs"
"Assets/Script/TeamInfo.cs"
"Assets/Script/TeamInfoEditor.cs"
"Assets/Script/TideWalkerTracker.cs"
"Assets/Script/UI/BuoyPlacementUI.cs"
"Assets/Script/UI/SimpleBuoyUI.cs"
"Assets/Script/UI/SliderTextUpdater.cs"
"Assets/Script/UIBoatEntry.cs"
"Assets/Script/UIBoatSelection.cs"
"Assets/Script/UIDisplayGeneralRanking.cs"
"Assets/Script/UIDisplayResults.cs"
"Assets/Script/UIRanking.cs"
"Assets/Script/UIRankingEntry.cs"
"Assets/Script/UISessionResultsPanel.cs"
"Assets/Script/UI_PanelController.cs"
"Assets/Script/UnifiedBoatGPS.cs"
"Assets/Script/UnityMainThreadDispatcher.cs"
"Assets/Script/VectorExtension.cs"
"Assets/Script/WaterTrailAdjuster.cs"
"Assets/Script/WindParticleController.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark01.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark01_UGUI.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark02.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark03.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark04.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/CameraController.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/ChatController.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/DropdownSample.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/EnvMapAnimator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/ObjectSpin.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/ShaderPropAnimator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/SimpleScript.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/SkewTextExample.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TeleType.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TextConsoleSimulator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TextMeshProFloatingText.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TextMeshSpawner.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMPro_InstructionOverlay.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_DigitValidator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_ExampleScript_01.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_FrameRateCounter.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_PhoneNumberValidator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextEventCheck.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextEventHandler.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextInfoDebugTool.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextSelector_A.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextSelector_B.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_UiFrameRateCounter.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexColorCycler.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexJitter.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexShakeA.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexShakeB.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexZoom.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/WarpTextExample.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1900b0aP.dag/Assembly-CSharp.UnityAdditionalFile.txt"