using UnityEngine;
using System.Collections;

public class CameraDirector : MonoBeh<PERSON>our
{
    [SerializeField] private SpectatorCameraController cameraController;
    [SerializeField] private RaceController raceController;

    private SpectatorCameraController.CameraMode currentMode;

    private void Start()
    {
        if (cameraController == null)
            cameraController = FindObjectOfType<SpectatorCameraController>();

        if (raceController == null)
            raceController = FindObjectOfType<RaceController>();

        // Commencer avec la vue Follow
        SetCameraMode(SpectatorCameraController.CameraMode.Follow);
    }

    private void SetCameraMode(SpectatorCameraController.CameraMode newMode)
    {
        if (cameraController != null && currentMode != newMode)
        {
            currentMode = newMode;
            cameraController.SetCameraMode(newMode);
        }
    }

    // Events de course si n�cessaire
    public void OnRaceStart()
    {
        SetCameraMode(SpectatorCameraController.CameraMode.Follow);
    }

    public void OnRaceEnd()
    {
        SetCameraMode(SpectatorCameraController.CameraMode.Overhead);
    }
}